import { useIntercessorStore } from '~/stores/intercessor'
import { IntercessorCollection } from '~/utils/appwrite'

export const useIntercessor = () => {
  const intercessorStore = useIntercessorStore()
  const intercessorCollection = new IntercessorCollection()

  const hydrate = async () => {
    intercessorStore.intercessors = (await intercessorCollection.get()) || []
  }

  const create = async (data: any) => {
    const intercessor = await intercessorCollection.create(data)
    intercessorStore.intercessors.push(intercessor)
  }

  const remove = async (id: string) => {
    await intercessorCollection.delete(id)
    intercessorStore.intercessors = intercessorStore.intercessors.filter(intercessor => intercessor.$id !== id)
  }

  return { hydrate, create, remove }
}
