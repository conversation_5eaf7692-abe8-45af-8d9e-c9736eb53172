import { useSponsorshipStore } from '~/stores/sponsorship'
import { SponsorshipCollection } from '~/utils/appwrite'

export const useSponsorship = () => {
  const sponsorshipStore = useSponsorshipStore()
  const sponsorshipCollection = new SponsorshipCollection()

  const hydrate = async () => {
    sponsorshipStore.sponsorships = (await sponsorshipCollection.get()) || []
  }

  const create = async (data: any) => {
    const sponsorship = await sponsorshipCollection.create(data)
    sponsorshipStore.sponsorships.push(sponsorship)
  }

  const remove = async (id: string) => {
    await sponsorshipCollection.delete(id)
    sponsorshipStore.sponsorships = sponsorshipStore.sponsorships.filter(sponsorship => sponsorship.$id !== id)
  }

  return { hydrate, create, remove }
}
