import { ref } from 'vue'
import { FileCollection, StorageCollection } from '~/utils/appwrite'

export const useFile = () => {
  const fileCollection = new FileCollection()
  const loading = ref(false)
  const error = ref<string | null>(null)

  /**
   * Get file record by ID
   * @param id The ID to look up files for
   */
  const getFileRecord = async (id: string) => {
    try {
      loading.value = true
      error.value = null

      // Find the file record for this ID
      const records = await fileCollection.get()
      return records?.find(record => record.id === id) || null
    } catch (err) {
      console.error('Error getting file record:', err)
      error.value = err instanceof Error ? err.message : 'Failed to get file record'
      return null
    } finally {
      loading.value = false
    }
  }

  /**
   * Create or update a file record
   * @param id The ID to associate files with
   * @param fileIds Array of file IDs
   */
  const saveFileRecord = async (id: string, fileIds: string[]) => {
    try {
      loading.value = true
      error.value = null

      // Check if a record already exists for this ID
      const existingRecord = await getFileRecord(id)

      if (existingRecord) {
        // Update existing record
        return await fileCollection.update(existingRecord.$id, {
          id,
          attachments: fileIds
        })
      } else {
        // Create new record
        return await fileCollection.create({
          id,
          attachments: fileIds
        })
      }
    } catch (err) {
      console.error('Error saving file record:', err)
      error.value = err instanceof Error ? err.message : 'Failed to save file record'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * Upload files and associate them with an ID
   * @param id The ID to associate files with
   * @param files The files to upload
   * @param allowMultiple Whether to allow multiple files (if false, existing files will be deleted)
   */
  const uploadFiles = async (id: string, files: File[], allowMultiple: boolean = true) => {
    try {
      loading.value = true
      error.value = null

      // Get existing record if any
      const existingRecord = await getFileRecord(id)
      let existingFileIds = existingRecord?.attachments || []

      // If multiple is false, delete existing files
      if (!allowMultiple && existingFileIds.length > 0) {
        // Delete each existing file from storage
        const deletePromises = existingFileIds.map((fileId: string) => StorageCollection.delete(fileId))
        await Promise.all(deletePromises)

        // Clear the existing file IDs
        existingFileIds = []
      }

      // Upload each file
      const uploadPromises = Array.from(files).map(async (file) => {
        const result = await StorageCollection.create(file)
        return result?.$id
      })

      const newFileIds = (await Promise.all(uploadPromises)).filter(Boolean) as string[]

      // Combine existing and new file IDs
      const allFileIds = [...existingFileIds, ...newFileIds]

      // Save the record
      await saveFileRecord(id, allFileIds)

      return newFileIds
    } catch (err) {
      console.error('Error uploading files:', err)
      error.value = err instanceof Error ? err.message : 'Failed to upload files'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * Delete a file
   * @param id The ID associated with the file record
   * @param fileId The file ID to delete
   */
  const deleteFile = async (id: string, fileId: string) => {
    try {
      loading.value = true
      error.value = null

      // Delete from storage
      await StorageCollection.delete(fileId)

      // Update the record
      const existingRecord = await getFileRecord(id)

      if (existingRecord) {
        const updatedFileIds = existingRecord.attachments.filter((fId: string) => fId !== fileId)
        await saveFileRecord(id, updatedFileIds)
      }

      return true
    } catch (err) {
      console.error('Error deleting file:', err)
      error.value = err instanceof Error ? err.message : 'Failed to delete file'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * Get file URL for viewing
   * @param fileId The file ID
   */
  const getFileUrl = (fileId: string) => {
    return StorageCollection.getFileUrl(fileId)
  }

  /**
   * Get file preview URL
   * @param fileId The file ID
   */
  const getFilePreviewUrl = (fileId: string) => {
    return StorageCollection.getFilePreviewUrl(fileId)
  }

  return {
    loading,
    error,
    getFileRecord,
    uploadFiles,
    deleteFile,
    getFileUrl,
    getFilePreviewUrl
  }
}
