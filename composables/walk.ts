import { useWalkStore } from '~/stores/walk'
import { WalkCollection } from '~/utils/appwrite'

export const useWalk = () => {
  const walkStore = useWalkStore()
  const walkCollection = new WalkCollection()

  const hydrate = async () => {
    walkStore.walks = (await walkCollection.get()) || []
  }

  const create = async (data: any) => {
    const walk = await walkCollection.create(data)
    walkStore.walks.push(walk)
  }

  const update = async (id: string, data: any) => {
    // Remove the type attribute from data, as it's not a valid attribute for the walk collection
    delete data.type
    const walk = await walkCollection.update(id, data)
    walkStore.walks = walkStore.walks.map(w => w.$id === id ? walk : w)
  }

  const remove = async (id: string) => {
    await walkCollection.delete(id)
    walkStore.walks = walkStore.walks.filter(walk => walk.$id !== id)
  }

  const getTimeSlots = (walk: any) => {
    const start     = new Date(walk.start_at)
    const end       = new Date(walk.end_at)
    const timeSlots = []

    for (let time = start; time <= end; time.setHours(time.getHours() + 1)) {
      timeSlots.push(new Date(time))
    }

    return timeSlots
  }

  return { hydrate, create, update, remove, getTimeSlots }
}
