import { usePilgrimStore } from '~/stores/pilgrim'
import { PilgrimCollection } from '~/utils/appwrite'

export const usePilgrim = () => {
  const pilgrimStore = usePilgrimStore()
  const pilgrimCollection = new PilgrimCollection()

  const hydrate = async () => {
    pilgrimStore.pilgrims = (await pilgrimCollection.get()) || []
  }

  const create = async (data: any) => {
    const pilgrim = await pilgrimCollection.create(data)
    pilgrimStore.pilgrims.push(pilgrim)
  }

  const remove = async (id: string) => {
    await pilgrimCollection.delete(id)
    pilgrimStore.pilgrims = pilgrimStore.pilgrims.filter(pilgrim => pilgrim.id !== id)
  }

  return { hydrate, create, remove }
}
