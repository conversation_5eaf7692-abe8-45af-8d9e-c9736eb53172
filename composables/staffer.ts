import { useStafferStore } from '~/stores/staffer'
import { StafferCollection } from '~/utils/appwrite'

export const useStaffer = () => {
  const stafferStore = useStafferStore()
  const stafferCollection = new StafferCollection()

  const hydrate = async () => {
    stafferStore.staffers = (await stafferCollection.get()) || []
  }

  const create = async (data: any) => {
    const staffer = await stafferCollection.create(data)
    stafferStore.staffers.push(staffer)
  }

  const remove = async (id: string) => {
    await stafferCollection.delete(id)
    stafferStore.staffers = stafferStore.staffers.filter(staffer => staffer.$id !== id)
  }

  return { hydrate, create, remove }
}
