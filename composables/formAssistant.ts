export const useFormAssistant = () => {
  const state = reactive({
    status: 'idle' as 'idle' | 'pending' | 'success' | 'error',
    message: '',
  })

  const areRequiredFieldsFilled = (requiredFields: string[], form: Record<string, any>) => {
    return requiredFields.every(field => {
      const value = form[field]
      if (Array.isArray(value)) {
        return value.length > 0
      }
      if (value && typeof value === 'object') {
        return Object.values(value).every(subValue => subValue !== '' && subValue !== null)
      }
      return value !== '' && value !== null
    })
  }

  return {
    state,
    areRequiredFieldsFilled
  }
}
