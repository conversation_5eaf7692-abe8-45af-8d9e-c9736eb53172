import { XlsxExporter } from '~/utils/xlsx-exporter';
import { useWalkStore } from '~/stores/walk';
import { usePilgrimStore } from '~/stores/pilgrim';
import { useSponsorshipStore } from '~/stores/sponsorship';
import { useStafferStore } from '~/stores/staffer';
import { useIntercessorStore } from '~/stores/intercessor';

export const useExporter = () => {
  const walkStore = useWalkStore();
  const pilgrimStore = usePilgrimStore();
  const sponsorshipStore = useSponsorshipStore();
  const stafferStore = useStafferStore();
  const intercessorStore = useIntercessorStore();

  /**
   * Export walk data to a file
   * @param walkId The ID of the walk to export
   * @param format The format to export to (default: 'xlsx')
   */
  const exportWalk = (walkId: string, format: 'xlsx' | 'csv' | 'pdf' = 'xlsx') => {
    // Find the walk
    const walk = walkStore.walks.find(w => w.$id === walkId);
    if (!walk) return;

    // Get related data
    const pilgrims = pilgrimStore.pilgrims.filter(p => p.walk_id === walkId);
    const sponsorships = sponsorshipStore.sponsorships.filter(s => s.walk_id === walkId);
    const staffers = stafferStore.staffers.filter(s => s.walk_id === walkId);
    const intercessors = intercessorStore.intercessors.filter(i => i.walk_id === walkId);

    // Export based on format
    switch (format) {
      case 'xlsx':
        XlsxExporter.exportWalk(walkId, walk, { pilgrims, sponsorships, staffers, intercessors });
        break;
      case 'csv':
        // Future implementation for CSV export
        console.log('CSV export not yet implemented');
        break;
      case 'pdf':
        // Future implementation for PDF export
        console.log('PDF export not yet implemented');
        break;
      default:
        // Default to XLSX
        XlsxExporter.exportWalk(walkId, walk, { pilgrims, sponsorships, staffers, intercessors });
    }
  };

  return {
    exportWalk
  };
};
