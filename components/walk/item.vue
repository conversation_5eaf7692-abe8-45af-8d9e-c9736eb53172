<template>
  <LayoutTimelineEvent>
    <template #date>
      <ElementDate :value="props.walk.start_at" format="MMMM DD" />
    </template>
    <template #time>
      <ElementDate :value="props.walk.start_at" format="hia" />
    </template>
    <template #timeline-icon>
      <i class="fa-sharp fa-solid fa-person-walking"></i>
    </template>

    <section class="what-section">
      <section class="name-section">
        <div>{{ props.walk.name }}</div>
        <ElementChip v-if="spotsLeft <= 5">
          <i class="fa-sharp fa-solid fa-bell"></i> {{ spotsLeft }} spots left
        </ElementChip>
      </section>

      <section class="address-section" v-if="props.walk.address">
        <ElementWithIcon>
          <template #icon>
            <i class="fa-sharp fa-regular fa-location-dot"></i>
          </template>
          <ElementAddress :address="props.walk.address" />
        </ElementWithIcon>
      </section>

      <div class="notes-section" v-if="props.walk.notes" v-html="notesAsMd"></div>

      <footer class="footer">
        <DropdownMenuRoot v-model:open="isDropdownMenuVisible">
          <DropdownMenuTrigger class="button" data-flavor="primary" aria-label="Customise options">
            Sign Up <i class="fa-sharp fa-solid fa-caret-down ml-05"></i>
          </DropdownMenuTrigger>

          <DropdownMenuPortal>
            <DropdownMenuContent class="DropdownMenuContent" :side-offset="5">
              <DropdownMenuLabel class="DropdownMenuLabel">
                Experience the Walk
              </DropdownMenuLabel>
              <DropdownMenuItem value="Pilgrim" class="DropdownMenuItem" @click="togglePopover(`pilgrim-popover-${props.walk.$id}`)">
                Pilgrim&hellip; <div class="RightSlot" v-if="pilgrimsForWalk.length > 0">{{ pilgrimsForWalk.length }}</div>
              </DropdownMenuItem>
              <DropdownMenuSeparator class="DropdownMenuSeparator" />

              <DropdownMenuLabel class="DropdownMenuLabel">
                Contribute
              </DropdownMenuLabel>
              <DropdownMenuItem value="Sponsor" class="DropdownMenuItem" @click="togglePopover(`sponsorship-popover-${props.walk.$id}`)">
                Sponsor&hellip; <div class="RightSlot" v-if="sponsorshipsForWalk.length > 0">{{ sponsorshipsForWalk.length }}</div>
              </DropdownMenuItem>
              <DropdownMenuItem value="Staffer" class="DropdownMenuItem" @click="togglePopover(`staffer-popover-${props.walk.$id}`)">
                Staffer&hellip; <div class="RightSlot" v-if="staffersForWalk.length > 0">{{ staffersForWalk.length }}</div>
              </DropdownMenuItem>
              <DropdownMenuItem value="Intercessors" class="DropdownMenuItem" @click="togglePopover(`intercessor-popover-${props.walk.$id}`)">
                Intercessor&hellip; <div class="RightSlot" v-if="intercessorsForWalk.length > 0">{{ intercessorsForWalk.length }}</div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenuPortal>
        </DropdownMenuRoot>

        <div class="flex-space"></div>

        <div class="walk-number font-small font-caps font-bold">#{{ props.walk.number }}</div>
      </footer>

      <LayoutAdminToolbar class="event-management-tools" v-if="userStore.user">
        <button
          class="button"
          type="button"
          @click="onExport">
          <i class="fa-sharp fa-regular fa-file-export"></i>
        </button>
        <button
          class="button"
          type="button"
          :id="`edit-walk-trigger-${props.walk.$id}`"
          :popovertarget="`edit-walk-popover-${props.walk.$id}`">
          <i class="fa-sharp fa-regular fa-pencil"></i>
        </button>
        <button
          class="button"
          type="button"
          @click="onDelete">
          <i class="fa-sharp fa-regular fa-trash text-red"></i>
        </button>
        <LazyWalkEdit :walk="props.walk" />
      </LayoutAdminToolbar>
    </section>

    <LazySponsorshipAdd :walk="props.walk" />
    <LazyPilgrimAdd :walk="props.walk" />
    <LazyStafferAdd :walk="props.walk" />
    <LazyIntercessorAdd :walk="props.walk" />

  </LayoutTimelineEvent>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { useUserStore } from '~/stores/user'
const userStore = useUserStore()

const props = defineProps({
  walk: {
    type: Object as PropType<WalkInterface>,
    required: true,
  }
})

// Sponsorships
import { useSponsorshipStore } from '~/stores/sponsorship'
const sponsorshipStore = useSponsorshipStore()

const sponsorshipsForWalk = computed(() => {
  return sponsorshipStore.sponsorships.filter(sponsorship => sponsorship.walk_id === props.walk.$id)
})

// Pilgrims
import { usePilgrimStore } from '~/stores/pilgrim'
const pilgrimStore = usePilgrimStore()

const pilgrimsForWalk = computed(() => {
  return pilgrimStore.pilgrims.filter(pilgrim => pilgrim.walk_id === props.walk.$id)
})

// Staffers
import { useStafferStore } from '~/stores/staffer'
const stafferStore = useStafferStore()

const staffersForWalk = computed(() => {
  return stafferStore.staffers.filter(staffer => staffer.walk_id === props.walk.$id)
})

// Intercessors
import { useIntercessorStore } from '~/stores/intercessor'
const intercessorStore = useIntercessorStore()

const intercessorsForWalk = computed(() => intercessorStore.intercessors.filter(intercessor => intercessor.walk_id === props.walk.$id))

// Walk
import { useWalk } from '~/composables/walk'
import type { WalkInterface } from '~/utils/appwrite'
const walk = useWalk()

const onDelete = () => {
  walk.remove(props.walk.$id)
}

const spotsLeft = computed(() => {
  return props.walk.max_participants - pilgrimsForWalk.value.length
})

// Dropdown Menu
import { ref } from 'vue'
import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuRoot,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from 'radix-vue'

const isDropdownMenuVisible = ref(false)

const togglePopover = (popoverId: string) => {
  const popover = document.getElementById(popoverId) as HTMLElement & { togglePopover: () => void }
  popover?.togglePopover()
}

// Markdown
import markdownit from 'markdown-it'
const md = markdownit()

const notesAsMd = computed(() => md.render(props.walk.notes))

// Export
import { useExporter } from '~/composables/exporter'
const exporter = useExporter()

const onExport = () => {
  exporter.exportWalk(props.walk.$id)
}
</script>

<style scoped>
.what-section {
  position: relative;

  container-type: inline-size;

  display: flex;
  flex-direction: column;
  gap: 1px;

  & > * {
    padding: 1rem;
    background: light-dark(oklch(from black l c h / 0.05), oklch(from white l c h / 0.1));
  }
}

.name-section {
  display: flex;
  justify-content: space-between;
  gap: 1.5rem;

  font-weight: bold;
}

.notes-section {
  > * {
    leading-trim: both;
    text-edge: cap alphabetic;
  }
}

.footer {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  width: 100%;
}

.walk-number {
  opacity: 0.5;
}

.event-management-tools {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;

  opacity: 0;
  transform: translateY(0.5lh);
  transition: opacity 0.2s ease, transform 0.2s ease;
  will-change: opacity, transform;
}

.what-section:hover .event-management-tools {
  opacity: 1;
  transform: translateY(0);
}
</style>
