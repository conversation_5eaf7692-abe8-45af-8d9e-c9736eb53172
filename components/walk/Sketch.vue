<template>
  <div class="event-sketch form-layout">
    <ElementWithIcon class="span-75">
      <template #icon>
        <i class="fa-sharp fa-solid fa-person-walking"></i>
      </template>
      <div>
        <div>{{ props.walk?.name }}</div>
        <ElementDate :value="props.walk?.start_at" format="MMMM DDD" />
      </div>
    </ElementWithIcon>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps<{
  walk: object
}>()
</script>

<style scoped>
.event-sketch {
  padding-block: 1rem;
  border-block-start: 1px solid oklch(from var(--label-background-color) l c h / 0.1);
  border-block-end: 1px solid oklch(from var(--label-background-color) l c h / 0.1);
}
</style>
