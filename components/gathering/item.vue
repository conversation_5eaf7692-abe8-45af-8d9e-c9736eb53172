<template>
  <LayoutTimelineEvent>
    <template #date>
      {{ new Date(props.gathering.start_at).toLocaleString('en-US', { month: 'long', day: 'numeric' }) }}
    </template>
    <template #time>
      {{ new Date(props.gathering.start_at).toLocaleTimeString('en-US', { hour: 'numeric', minute: 'numeric', hour12: true }) }} &ndash;
      {{ new Date(props.gathering.end_at).toLocaleTimeString('en-US', { hour: 'numeric', minute: 'numeric', hour12: true }) }}
    </template>
    <template #timeline-icon>
      <i class="fa-sharp fa-solid fa-fire"></i>
    </template>

    <section class="what-section">
      <section class="name font-bold">{{ props.gathering.name }}</section>

      <ElementWithIcon v-if="props.gathering.address">
        <template #icon>
          <i class="fa-sharp fa-regular fa-location-dot"></i>
        </template>
        <ElementAddress :address="props.gathering.address" />
      </ElementWithIcon>

      <section v-if="props.gathering.notes" v-html="notesAsMd"></section>

      <LayoutAdminToolbar class="event-management-tools" v-if="userStore.user">
        <button
          class="button"
          type="button"
          :id="`edit-gathering-trigger-${props.gathering.$id}`"
          :popovertarget="`edit-gathering-popover-${props.gathering.$id}`">
          <i class="fa-sharp fa-regular fa-pencil"></i>
        </button>
        <button
          class="button"
          type="button"
          @click="onDelete">
          <i class="fa-sharp fa-regular fa-trash text-red"></i>
        </button>
        <LazyGatheringEdit :gathering="props.gathering" />
      </LayoutAdminToolbar>
    </section>
  </LayoutTimelineEvent>
</template>

<script lang="ts" setup>
import { useGathering } from '~/composables/gathering'
import { useUserStore } from '~/stores/user'
const userStore = useUserStore()

const gatheringComposable = useGathering()

const props = defineProps({
  gathering: {
    type: Object as PropType<WalkInterface>,
    required: true,
  }
})

// Markdown
import markdownit from 'markdown-it'
import { computed } from 'vue'
const md = markdownit()

const notesAsMd = computed(() => md.render(props.gathering.notes))

const onDelete = async () => {
  await gatheringComposable.remove(props.gathering.$id)
}
</script>

<style scoped>
.what-section {
  position: relative;

  --background: light-dark(oklch(from black l c h / 0.05), oklch(from white l c h / 0.1));

  display: flex;
  flex-direction: column;
  gap: 1rem;

  padding-block-start: 1rem;
}

.event-management-tools {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;

  opacity: 0;
  transform: translateY(0.5lh);
  transition: opacity 0.2s ease, transform 0.2s ease;
  will-change: opacity, transform;
}

.what-section:hover .event-management-tools {
  opacity: 1;
  transform: translateY(0);
}
</style>
