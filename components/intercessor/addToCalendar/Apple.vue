<template>
  <button class="flex justify-between items-center gap" type="button" @click="handleExport">
    Apple <i class="fa-brands fa-apple"></i>
  </button>
</template>

<script lang="ts" setup>
interface Props {
  walk: {
    name: string;
  };
  intercessor: {
    start_at: Date[];
  };
}

const props = defineProps<Props>()

const handleExport = () => {
  // Generate a unique ID for the calendar events
  const uid = `mid-michigan-emmaus-${Date.now()}`

  // Create ICS content for each date
  props.intercessor.start_at.forEach((date: Date) => {
    // Format the date for ICS format
    const startDate = new Date(date)
    const endDate = new Date(startDate)
    endDate.setHours(startDate.getHours() + 1) // 1-hour duration

    // Format dates for ICS (YYYYMMDDTHHmmssZ)
    const formatDate = (date: Date) => {
      return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z'
    }

    // Create ICS content
    const icsContent = [
      'BEGIN:VCALENDAR',
      'VERSION:2.0',
      'PRODID:-//Mid Michigan Emmaus//Walk Prayer//EN',
      'BEGIN:VEVENT',
      `UID:${uid}-${startDate.getTime()}`,
      `DTSTAMP:${formatDate(new Date())}`,
      `DTSTART:${formatDate(startDate)}`,
      `DTEND:${formatDate(endDate)}`,
      `SUMMARY:Prayer Time: ${props.walk.name}`,
      `DESCRIPTION:Please pray for the Emmaus Walk participants during this time.`,
      'BEGIN:VALARM',
      'TRIGGER:-PT10M',
      'ACTION:DISPLAY',
      'DESCRIPTION:Reminder',
      'END:VALARM',
      'END:VEVENT',
      'END:VCALENDAR'
    ].join('\r\n')

    // Create blob and download
    const blob = new Blob([icsContent], { type: 'text/calendar;charset=utf-8' })
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.setAttribute('download', `prayer-time-${formatDate(startDate)}.ics`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  })
}
</script>

<style>

</style>
