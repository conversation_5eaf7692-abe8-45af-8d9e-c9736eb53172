<template>
  <details ref="detailsRef" @focusout="handleFocusOut">
    <summary class="button flex justify-between items-center gap-2">
      Add to calendar &nbsp; <i class="fa-sharp fa-solid fa-caret-down"></i>
    </summary>

    <div class="expanded-details">
      <IntercessorAddToCalendarApple :intercessor="props.intercessor" :walk="props.walk" />
      <IntercessorAddToCalendarGoogle :intercessor="props.intercessor" :walk="props.walk" />
      <IntercessorAddToCalendarOutlook :intercessor="props.intercessor" :walk="props.walk" />
    </div>
  </details>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

interface Props {
  walk: {
    name: string;
  };
  intercessor: {
    start_at: Date[];
  };
}

const props = defineProps<Props>()
const detailsRef = ref<HTMLDetailsElement | null>(null)

const handleFocusOut = (event: FocusEvent) => {
  // Check if the new focus target is outside the details element
  const relatedTarget = event.relatedTarget as HTMLElement
  if (detailsRef.value && !detailsRef.value.contains(relatedTarget)) {
    detailsRef.value.open = false
  }
}
</script>

<style scoped>
details {
  position: relative;
  outline: none; /* Remove default focus outline */
}

details:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

summary {
  list-style: none;
}

summary::-webkit-details-marker {
  display: none;
}

.expanded-details {
  position: absolute;
  left: 0;
  bottom: 100%;
  transform: translateY(-0.5rem);

  display: flex;
  flex-direction: column;
  gap: 0.25rem;

  background: light-dark(white, color-mix(in oklch, var(--background-color-dark), var(--background-color-light) 15%));
  padding: 0.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
}
</style>
