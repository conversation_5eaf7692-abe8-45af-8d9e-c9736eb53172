<template>
  <button class="flex justify-between items-center gap" type="button" @click="handleExport">
    Google <i class="fa-brands fa-google"></i>
  </button>
</template>

<script lang="ts" setup>
interface Props {
  walk: {
    name: string;
  };
  intercessor: {
    start_at: Date[];
  };
}

const props = defineProps<Props>()

const handleExport = () => {
  // Create Google Calendar URLs for each date
  props.intercessor.start_at.forEach((date: Date) => {
    // Format the date for Google Calendar URL
    const startDate = new Date(date)
    const endDate = new Date(startDate)
    endDate.setHours(startDate.getHours() + 1) // 1-hour duration

    // Format dates for Google Calendar URL (YYYYMMDDTHHmmssZ)
    const formatDate = (date: Date) => {
      return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z'
    }

    // Create Google Calendar URL
    const params = new URLSearchParams({
      action: 'TEMPLATE',
      text: `Prayer Time: ${props.walk.name}`,
      details: 'Please pray for the Emmaus Walk participants during this time.',
      dates: `${formatDate(startDate)}/${formatDate(endDate)}`,
      rem: 'PT10M' // 10-minute reminder
    })

    // Open Google Calendar in a new tab
    window.open(`https://calendar.google.com/calendar/render?${params.toString()}`, '_blank')
  })
}
</script>

<style>

</style>
