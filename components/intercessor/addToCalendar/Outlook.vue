<template>
  <button class="flex justify-between items-center gap" type="button" @click="handleExport">
    Outlook <i class="fa-brands fa-microsoft"></i>
  </button>
</template>

<script lang="ts" setup>
interface Props {
  walk: {
    name: string;
  };
  intercessor: {
    start_at: Date[];
  };
}

const props = defineProps<Props>()

const handleExport = () => {
  // Create Outlook Calendar URLs for each date
  props.intercessor.start_at.forEach((date: Date) => {
    // Format the date for Outlook Calendar URL
    const startDate = new Date(date)
    const endDate = new Date(startDate)
    endDate.setHours(startDate.getHours() + 1) // 1-hour duration

    // Format dates for Outlook Calendar URL (YYYY-MM-DDTHH:mm:ss)
    const formatDate = (date: Date) => {
      // Get the user's timezone
      const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone

      // Format the date in the user's timezone
      return new Intl.DateTimeFormat('en-US', {
        timeZone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).format(date).replace(/(\d+)\/(\d+)\/(\d+), (\d+):(\d+):(\d+)/, '$3-$1-$2T$4:$5:$6')
    }

    // Create Outlook Calendar URL
    const params = new URLSearchParams({
      subject: `Prayer Time: ${props.walk.name}`,
      body: 'Please pray for the Emmaus Walk participants during this time.',
      startdt: formatDate(startDate),
      enddt: formatDate(endDate),
      allday: 'false',
      location: '',
      uid: `mid-michigan-emmaus-${startDate.getTime()}`,
      path: '/calendar/action/compose',
      rru: 'addevent'
    })

    // Open Outlook Calendar in a new tab
    window.open(`https://outlook.live.com/calendar/0/deeplink/compose?${params.toString()}`, '_blank')
  })
}
</script>

<style>

</style>
