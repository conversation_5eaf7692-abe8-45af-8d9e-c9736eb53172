<template>
  <address>
    <div class="street" v-if="parsedAddress?.street">{{ parsedAddress.street }}</div>
    <div class="city-state-zip" v-if="parsedAddress?.city || parsedAddress?.state || parsedAddress?.zip">
      <template v-if="parsedAddress?.city">{{ parsedAddress.city }}</template>
      <template v-if="parsedAddress?.city && (parsedAddress?.state || parsedAddress?.zip)">, </template>
      <template v-if="parsedAddress?.state">{{ parsedAddress.state }}</template>
      <template v-if="parsedAddress?.zip">&nbsp;{{ parsedAddress.zip }}</template>
    </div>
  </address>
</template>

<script lang="ts" setup>
interface Address {
  street?: string
  city?: string
  state?: string
  zip?: string
}

const props = defineProps<{ address: Address | string }>()

const parsedAddress = computed(() => {
  if (!props.address) return null

  if (typeof props.address === 'string') {
    try {
      return JSON.parse(props.address)
    } catch (e) {
      return null
    }
  }

  return props.address
})
</script>

<style scoped>
.street,
.city-state-zip {
  text-wrap-mode: nowrap;
}
</style>
