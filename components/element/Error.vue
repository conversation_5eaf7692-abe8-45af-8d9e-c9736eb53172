<template>
  <ElementWithIcon class="element-error span-100" ref="element">
    <template #icon>
      <i class="fa-duotone fa-solid fa-circle-xmark text-red"></i>
    </template>
    <slot></slot>
  </ElementWithIcon>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue';

const element = ref<HTMLElement | null>(null);

onMounted(() => {
  nextTick(() => {
    if (element.value) {
      element.value.$el.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  });
});
</script>

<style scoped>
.element-error {
  background-color: oklch(from var(--pure-red) l c h / 0.1);
  padding: 1rem;
  border: 1px solid oklch(from var(--pure-red) l c h / 0.2);
  border-radius: 0.25rem;
}
</style>
