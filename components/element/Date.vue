<template>
  <span class="element-date">
    {{ formatDate(props.value) }}
  </span>
</template>

<script lang="ts" setup>
const props = defineProps<{
  value: object | string;
  format: string;
}>();

/**
 * Based on Moment.js format conventions
 * https://momentjs.com/docs/#/parsing/string-format/
 * @param format
 * @returns string
 */
function formatDate(value: object | string): string {
  const value_object = new Date(value);
  const options: Intl.DateTimeFormatOptions = {
    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
  };

  if (props.format.includes('YY')) options.year = '2-digit';
  if (props.format.includes('YYYY')) options.year = 'numeric';

  if (props.format.includes('MM')) options.month = '2-digit';
  if (props.format.includes('MMMM')) options.month = 'long';

  if (props.format.includes('D')) options.day = 'numeric';
  if (props.format.includes('DD')) options.day = '2-digit';

  if (props.format.includes('h')) options.hour = 'numeric';
  if (props.format.includes('i')) options.minute = 'numeric';
  if (props.format.includes('a')) options.hour12 = true;

  if (props.format.includes('ddd')) options.weekday = 'short';
  if (props.format.includes('dddd')) options.weekday = 'long';

  return new Intl.DateTimeFormat('en-US', options).format(value_object);
}
</script>

<style scoped>
/* .element-date {
  display: flex;
  gap: 0.5rem;
} */
</style>
