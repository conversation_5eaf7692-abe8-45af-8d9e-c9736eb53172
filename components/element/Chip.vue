<template>
  <div class="chip font-small">
    <pre><slot /></pre>
  </div>
</template>

<script lang="ts" setup>

</script>

<style scoped>
.chip {
  --size: 1.25rem;

  display: flex;
  justify-content: center;
  align-items: center;

  padding: 0 0.5rem;
  block-size: var(--size);
  text-wrap: nowrap;
  color: light-dark(color-mix(in oklch, var(--pink), white 75%), var(--pink));
  background-color: light-dark(var(--pink), oklch(from var(--pink) l c h / 0.075));
  border: 1px solid light-dark(color-mix(in oklch, var(--pink), white 75%), oklch(from var(--pink) l c h / 0.25));
  border-radius: 5rem;
  font-weight: bold;
}
</style>
