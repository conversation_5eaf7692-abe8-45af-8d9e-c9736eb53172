<template>
  <div class="form-layout">
    <label class="time-slot span-100"
      v-for="(slot, index) in time_slots"
      :key="index">

      <ElementDate class="weekday font-small font-caps" :data-visible="isWeekdayDifferentFromPrevious(index)" :value="slot" format="dddd" />
      <input type="checkbox" :value="slot" v-model="modelValue" />
      <ElementDate class="number-tabular" :value="slot" format="hi" />

      <section class="intercessors">
        <ElementChip
          v-for="intercessor in intercessorsForSlot(slot).slice(0, 1)"
          :key="intercessor.$id">
          {{ getFirstNameLastInitial(intercessor) }}
        </ElementChip>
        <ElementChip
          v-if="intercessorsForSlot(slot).length > 1"
          :title="intercessorsForSlot(slot).slice(1).map(i => getFirstNameLastInitial(i)).join('\n')">
          <span>+{{ intercessorsForSlot(slot).length - 1 }}</span>
        </ElementChip>
      </section>

    </label>
  </div>
</template>

<script lang="ts" setup>
const modelValue = defineModel<Array<string>>({
  type: Array,
  required: true
})

const props = defineProps({
  walk: {
    type: Object,
    required: true,
  }
})

const isWeekdayDifferentFromPrevious = (index: number) => {
  return index === 0 || time_slots[index].getDay() !== time_slots[index - 1].getDay();
}

// * Walk
import { useWalk } from '~/composables/walk'
const walk = useWalk()

const time_slots = walk.getTimeSlots(props.walk).map(slot => new Date(slot))

// * Intercessors
import { useIntercessorStore } from '~/stores/intercessor'
const intercessorStore = useIntercessorStore()
const intercessors_for_walk = computed(() => intercessorStore.intercessors.filter(intercessor => intercessor.walk_id === props.walk.$id))

const intercessorsForSlot = (slot: Date | string) => {
  const slot_date = typeof slot === "string" ? new Date(slot) : slot

  return intercessors_for_walk.value.filter((intercessor: { start_at: (Date | string)[] }) =>
    intercessor.start_at.some((date: Date | string) => {
      const dateObj = typeof date === "string" ? new Date(date) : date
      return dateObj.getTime() === slot_date.getTime()
    })
  )
}

const getFirstNameLastInitial = (intercessor: { name: string }) => {
  const [first, last] = intercessor.name.split(' ')
  return `${first} ${last[0]}`
}
</script>

<style scoped>
.time-slot {
  display: grid;
  grid-template-columns: 1fr auto 2fr 1fr;

  &:hover [data-visible] {
    opacity: 1;
  }
}

.intercessors {
  display: flex;
  justify-content: flex-end;
  gap: 0.2rem;

  width: 100%;
}

.weekday {
  font-weight: bold;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;

  &[data-visible="true"] {
    opacity: 1;
  }
}
</style>
