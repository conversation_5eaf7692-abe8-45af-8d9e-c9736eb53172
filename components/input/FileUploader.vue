<template>
  <div class="file-uploader">
    <div
      class="drop-zone"
      :class="{ 'drop-zone-active': isDragging, 'drop-zone-disabled': !isUserLoggedIn }"
      @dragover.prevent="onDragOver"
      @dragleave.prevent="onDragLeave"
      @drop.prevent="onDrop">

      <div
        v-if="isUserLoggedIn && (files.length === 0)"
        class="trigger-wrapper"
        @click="triggerFileInput">
        <template v-if="$slots.trigger">
          <slot name="trigger"></slot>
        </template>
        <template v-else>
          <button type="button" class="upload-button">
            <i class="fa-sharp fa-regular fa-upload"></i>
            Upload Files
          </button>
          <div class="drop-instructions">
            or drag and drop files here
          </div>
        </template>
      </div>

      <div
        v-if="files.length > 0"
        class="file-list"
        @click="triggerFileInput">
        <div v-for="file in files" :key="file.id" class="file-item">

          <!-- Custom file item template -->
          <template v-if="$slots.default">
            <slot
              :file="file"
              :url="getFileUrl(file.id)"
              :size="formatFileSize(file.size)"
              :format="file.format"
              :delete-file="isUserLoggedIn ? () => deleteFile(file.id) : null">
            </slot>
          </template>
          <template v-else>
            <!-- Default file item template -->
            <div class="file-info">
              <a :href="getFileUrl(file.id)" target="_blank" class="file-name">
                {{ file.name }}
                <span v-if="file.format" class="file-format">.{{ file.format }}</span>
              </a>
              <span class="file-size">{{ formatFileSize(file.size) }}</span>
            </div>
            <button
              v-if="isUserLoggedIn"
              type="button"
              class="delete-button"
              @click.stop="deleteFile(file.id)">
              <i class="fa-sharp fa-regular fa-trash"></i>
            </button>
          </template>
        </div>

        <!-- Drop instructions when files exist -->
        <div v-if="isUserLoggedIn" class="drop-instructions-overlay">
          <div class="drop-instructions">
            Drop files here to upload
          </div>
        </div>
      </div>
    </div>

    <!-- Hidden file input -->
    <input
      type="file"
      ref="fileInput"
      @change="handleFileChange"
      :multiple="multiple"
      :accept="accept"
      style="display: none" />

    <!-- Loading state -->
    <div v-if="loading" class="loading-indicator">
      <i class="fa-sharp fa-regular fa-spinner-third fa-spin"></i>
      Uploading...
    </div>

    <!-- Error message -->
    <div v-if="error" class="error-message">
      ERROR: {{ error }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useFile } from '~/composables/file'
import { useUserStore } from '~/stores/user'
import { StorageCollection } from '~/utils/appwrite'

const props = defineProps({
  // ID to associate files with (required)
  entityId: {
    type: String,
    required: true
  },
  // Allow multiple file uploads
  multiple: {
    type: Boolean,
    default: false
  },
  // File types to accept
  accept: {
    type: String,
    default: ''
  },
  // Maximum file size in bytes (default: 10MB)
  maxSize: {
    type: Number,
    default: 10 * 1024 * 1024
  }
})

const fileInput = ref(null)
const files = ref([])
const isDragging = ref(false)
const userStore = useUserStore()
const {
  loading,
  error,
  getFileRecord,
  uploadFiles,
  deleteFile: removeFile,
  getFileUrl: getUrl
} = useFile()

// Check if user is logged in
const isUserLoggedIn = computed(() => !!userStore.user)

// Load existing files on mount
onMounted(async () => {
  await loadFiles()
})

// Load files associated with this entity
const loadFiles = async () => {
  try {
    // Clear existing files first to avoid any stale data
    files.value = []

    const record = await getFileRecord(props.entityId)

    if (!record) {
      console.log('No file record found for entity ID:', props.entityId)
      return
    }

    if (!record.attachments || !Array.isArray(record.attachments) || record.attachments.length === 0) {
      console.log('No attachments found for entity ID:', props.entityId)
      return
    }

    // Fetch file metadata for each attachment
    for (const id of record.attachments) {
      if (!id) continue // Skip null or undefined IDs

      try {
        // Try to get file metadata from the storage service
        const fileInfo = await StorageCollection.getFileInfo(id)

        if (fileInfo) {
          // Extract file format from name
          const fileName = fileInfo.name
          const lastDotIndex = fileName.lastIndexOf('.')
          const format = lastDotIndex !== -1 ? fileName.substring(lastDotIndex + 1).toLowerCase() : ''
          const nameWithoutFormat = lastDotIndex !== -1 ? fileName.substring(0, lastDotIndex) : fileName

          files.value.push({
            id,
            name: nameWithoutFormat,
            format: format,
            size: fileInfo.size || 0
          })
        } else {
          // Fallback if metadata can't be retrieved
          files.value.push({
            id,
            name: `File ${id.substring(0, 8)}`,
            format: '',
            size: 0
          })
        }
      } catch (fileErr) {
        // Fallback if there's an error
        files.value.push({
          id,
          name: `File ${id.substring(0, 8)}`,
          format: '',
          size: 0
        })
        console.error('Error fetching file metadata:', fileErr)
      }
    }
  } catch (err) {
    console.error('Error loading files:', err)
  }
}

const triggerFileInput = () => {
  if (!isUserLoggedIn.value) return
  fileInput.value?.click()
}

// Process files for upload
const processFiles = async (fileList) => {
  if (!fileList || fileList.length === 0) {
    return
  }

  // Convert to array if it's a FileList
  const filesArray = Array.from(fileList)

  // Validate file sizes
  const validFiles = filesArray.filter(file => {
    if (file.size > props.maxSize) {
      error.value = `File ${file.name} exceeds the maximum size of ${formatFileSize(props.maxSize)}`
      return false
    }
    return true
  })

  if (validFiles.length === 0) {
    return
  }

  // Upload directly
  await uploadFilesWithNames(validFiles)
}

// Upload files
const uploadFilesWithNames = async (filesToUpload) => {
  try {
    // Upload files, passing the multiple prop to determine if existing files should be replaced
    const fileIds = await uploadFiles(props.entityId, filesToUpload, props.multiple)

    // If multiple is false, clear the local files array since all previous files were deleted
    if (!props.multiple) {
      files.value = []
    }

    // Add new files to the list
    fileIds.forEach((id, index) => {
      const file = filesToUpload[index]
      const fileName = file.name
      const lastDotIndex = fileName.lastIndexOf('.')
      const format = lastDotIndex !== -1 ? fileName.substring(lastDotIndex + 1).toLowerCase() : ''
      const nameWithoutFormat = lastDotIndex !== -1 ? fileName.substring(0, lastDotIndex) : fileName

      files.value.push({
        id,
        name: nameWithoutFormat,
        format: format,
        size: file.size
      })
    })
  } catch (err) {
    console.error('Error uploading files:', err)
  }
}

// Handle file selection from input
const handleFileChange = async (event) => {
  const input = event.target

  if (!input.files || input.files.length === 0) {
    return
  }

  await processFiles(input.files)

  // Clear the input
  input.value = ''
}

// Handle drag over
const onDragOver = () => {
  if (!isUserLoggedIn.value) return
  isDragging.value = true
}

// Handle drag leave
const onDragLeave = () => {
  if (!isUserLoggedIn.value) return
  isDragging.value = false
}

// Handle drop
const onDrop = async (event) => {
  if (!isUserLoggedIn.value) return
  isDragging.value = false

  if (!event.dataTransfer?.files) {
    return
  }

  // If multiple is false, only take the first file
  const filesToProcess = props.multiple
    ? event.dataTransfer.files
    : [event.dataTransfer.files[0]]

  await processFiles(filesToProcess)
}

// Delete a file
const deleteFile = async (fileId) => {
  try {
    await removeFile(props.entityId, fileId)
    files.value = files.value.filter(file => file.id !== fileId)
  } catch (err) {
    console.error('Error deleting file:', err)
  }
}

// Get file URL for viewing
const getFileUrl = (fileId) => {
  return getUrl(fileId)
}

// Format file size
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.file-uploader {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.drop-zone {
  position: relative;
  transition: all 0.2s ease;
}

.drop-zone-active {
  background-color: light-dark(
    oklch(from var(--pink) l c h / 0.1),
    oklch(from var(--pink) l c h / 0.2));
  border-radius: 0.6lh;
  box-shadow: 0 0 0 0.2lh color-mix(in oklch, var(--pink), transparent 20%);
}

.drop-zone-disabled {
  background-color: transparent;
  border: 0;
}

.trigger-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  height: 100%;
  min-height: 100px;
}

.upload-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: light-dark(oklch(from var(--pink) l c h / 0.1), oklch(from var(--pink) l c h / 0.2));
  border: 1px solid light-dark(var(--pink), oklch(from var(--pink) l c h / 0.5));
  border-radius: 0.25rem;
  color: light-dark(var(--pink), white);
  cursor: pointer;
}

.drop-instructions {
  font-size: 0.875rem;
  color: light-dark(oklch(from black l c h / 0.6), oklch(from white l c h / 0.6));
  margin-top: 0.5rem;
}

.drop-instructions-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: light-dark(oklch(from var(--pink) l c h / 0.05), oklch(from var(--pink) l c h / 0.1));
  border-radius: 0.5rem;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 1;
  pointer-events: none;
}

.drop-zone-active .drop-instructions-overlay {
  opacity: 1;
}

/* Default file item styling */
.file-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.file-name {
  color: light-dark(var(--pink), var(--pink));
  text-decoration: none;
}

.file-name:hover {
  text-decoration: underline;
}

.file-format {
  color: light-dark(oklch(from black l c h / 0.7), oklch(from white l c h / 0.7));
  font-weight: normal;
}

.file-size {
  font-size: 0.75rem;
  color: light-dark(oklch(from black l c h / 0.6), oklch(from white l c h / 0.6));
}

.delete-button {
  background: none;
  border: none;
  color: light-dark(var(--red), var(--red));
  cursor: pointer;
  padding: 0.25rem;
  z-index: 3;
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: light-dark(oklch(from black l c h / 0.6), oklch(from white l c h / 0.6));
}

.error-message {
  color: var(--red);
  font-size: 0.875rem;
}
</style>
