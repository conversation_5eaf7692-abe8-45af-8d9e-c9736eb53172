<template>
  <label>
    <CheckboxRoot v-model:checked="modelValue">
      <CheckboxIndicator>
        <Icon icon="radix-icons:check" />
      </CheckboxIndicator>
    </CheckboxRoot>
    <slot></slot>
  </label>
</template>

<script lang="ts" setup>
import { Icon } from '@iconify/vue'
import { CheckboxIndicator, CheckboxRoot } from 'radix-vue'

const modelValue = defineModel({
  modelValue: {
    type: Boolean,
    required: true
  }
})
</script>

<style>

</style>
