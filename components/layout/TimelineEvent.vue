<template>
  <div class="event-container">
    <section class="when-container">
      <div class="date font-bold">
        <slot name="date"></slot>
      </div>
      <div class="time">
        <slot name="time"></slot>
      </div>
    </section>

    <section class="timeline-container">
      <div class="timeline-point">
        <slot name="timeline-icon"></slot>
      </div>
    </section>

    <section class="what-container">
      <slot></slot>
    </section>
  </div>
</template>

<script lang="ts" setup>

</script>

<style scoped>
.event-container {
  --offset: 1rlh;

  display: grid;
  grid-template-columns: 25% 1px calc(75% - 1px);
  grid-template-areas: "when timeline what";
}

.when-container {
  grid-area: when;

  padding-block-start: calc(var(--offset) * 3);
  padding-inline-end: 2rlh;
  text-align: right;
}

.timeline-container {
  --timeline-color: var(--red);

  grid-area: timeline;

  background: var(--timeline-color);

  .timeline-point {
    display: grid;
    place-items: center;

    inline-size: 2rlh;
    block-size: 2rlh;
    margin-block-start: calc(var(--offset) * 3);
    background-color: var(--timeline-color);
    border: 1px solid var(--timeline-color);
    border-radius: 3rlh;

    transform: translateX(-50%);
  }
}

.what-container {
  grid-area: what;

  margin-inline-start: calc(var(--offset) * 2);
  margin-block-start: calc(var(--offset) * 2);
  margin-block-end: calc(var(--offset) * 2);
  inline-size: clamp(0rem, 100%, 45rem);
  border-radius: 0.5rem;
  overflow: clip;
}
</style>
