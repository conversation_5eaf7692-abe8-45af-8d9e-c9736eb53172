<template>
  <div
    id="admin-toolbar"
    v-if="userStore.user"
    layoutId="admin-toolbar"
    :initial="{ opacity: 0, y: 10 }"
    :animate="{ opacity: 1, y: 0 }"
    :exit="{ opacity: 0, y: 10 }"
    :transition="{ type: 'spring', damping: 20, stiffness: 300 }">
    <slot />
  </div>
</template>

<script lang="ts" setup>
import { useUserStore } from '~/stores/user'

const userStore = useUserStore()
</script>

<style scoped>
#admin-toolbar {
  --background-color-light: color-mix(in oklch, var(--pure-gray) 10%, var(--pure-silver));
  --background-color-dark: color-mix(in oklch, var(--pure-gray) 10%, var(--pure-black));
  --background-color: light-dark(var(--background-color-light), var(--background-color-dark));

  display: flex;
  align-items: center;

  inline-size: fit-content;
  padding: 0.5rem;
  backdrop-filter: blur(1rem);
  background: color-mix(in oklch, var(--background-color) 50%, transparent);
  border: 1px solid color-mix(in oklch, white 20%, transparent);
  border-radius: 3rem;
}
</style>
