<template>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/normalize-opentype.css/0.2.4/normalize-opentype.css" integrity="sha512-XBLUJy48hph6E/wRP2rTqI6HJxw/47e9XIOlB0dG8YWAsJANGVr+YdcgWGDYsQPSRFfeD8sEVEwGklKQA7gB7w==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://kit.fontawesome.com/4dcc40e235.css" crossorigin="anonymous">

  <nav id="site-navigation-wrapper">
    <LayoutContentContainer id="site-navigation">
      <section class="primary-section">
        <NuxtLink href="/" class="font-small font-bold font-caps" :data-active="route.name === 'index'">
          Mid-Michigan Emmaus
          <!-- <img class="brand" data-variant="black" src="/images/brand.svg" alt="Mid-Michigan Emmaus" /> -->
          <!-- <img class="brand" data-variant="white" src="/images/brand-white.svg" alt="Mid-Michigan Emmaus" /> -->
        </NuxtLink>
      </section>

      <section class="secondary-section">
        <span>
          <NuxtLink href="/events" class="font-small font-bold font-caps" :data-active="route.name === 'events'">
            Events
          </NuxtLink>
        </span>
        <span>
          <NuxtLink href="/chrysalis" class="font-small font-bold font-caps" :data-active="route.name === 'chrysalis'">
            Chrysalis
          </NuxtLink>
        </span>

        <div class="flex-space"></div>

        <span>
          <NuxtLink href="/contact" class="font-small font-bold font-caps" :data-active="route.name === 'board'">
            Contact
          </NuxtLink>
        </span>
        <span>
          <AuthHandler />
        </span>
      </section>
    </LayoutContentContainer>
  </nav>

  <main>
    <NuxtPage />
  </main>

  <footer id="site-footer-wrapper">
    <LayoutContentContainer id="site-footer">
      <img class="brand" src="/images/brand-gold.svg" alt="Mid-Michigan Emmaus" />
      <address class="address font-small">
        <span class="font-bold">Mid-Michigan Emmaus</span><br/>
        c/o Aldersgate Church<br/>
        2206 Airfield Lane<br/>
        Midland, MI 48642
      </address>
      <div class="other font-small">
        <p>Have a question? <NuxtLink href="/contact">We want to hear from you.</NuxtLink></p>
        <p>&copy; {{ new Date().getFullYear() }} Mid-Michigan Emmaus</p>
      </div>
    </LayoutContentContainer>
  </footer>
</template>

<script lang="ts" setup>
const route = useRoute()
</script>

<style>
@import url("~/assets/styles/main.css");

html {
  --green-gradient: light-dark(oklch(from var(--dark-green) l c h / 0.5), oklch(from var(--dark-green) l c h / 0.4));
  --red-gradient: light-dark(oklch(from var(--red) l c h / 0.2), oklch(from var(--red) l c h / 0.4));

  color: var(--root-color);
  background-color: var(--root-background);
  background-image:
    radial-gradient(circle at 20% top, var(--green-gradient) 0%, transparent 60rem),
    radial-gradient(circle at 90% top, var(--red-gradient) 0%, transparent 30rem),

    radial-gradient(circle at 20% bottom, var(--red-gradient) 0%, transparent 60rem),
    radial-gradient(circle at 90% bottom, var(--green-gradient) 0%, transparent 40rem);
}

#site-navigation-wrapper {
  position: sticky;
  top: 5rlh;
  left: 0;

  width: 100dvw;
  z-index: 10;

  *:not(a) {
    user-select: none;
  }
}

#site-navigation {
  display: grid;
  grid-template-columns: 1fr 1fr;

  pointer-events: none;

  a, button {
    border-bottom: 0;
    pointer-events: auto;
  }

  .brand {
    &[data-variant="black"] {
      display: block;
      @media (prefers-color-scheme: dark) {
        display: none;
      }
    }

    &[data-variant="white"] {
      display: none;
      @media (prefers-color-scheme: dark) {
        display: block;
      }
    }
  }

  .primary-section {
    display: flex;
    justify-content: flex-start;

    .brand {
      width: 10rem;
      aspect-ratio: 1 / 1;
    }
  }

  .secondary-section {
    display: flex;
    gap: 2rem;

    /* Remove slight offset introduced by containing span. */
    span {
      display: inline;
    }
  }
}

#site-footer {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-areas: "brand ... address other";
  gap: 2rem;
  align-items: center;

  margin-block: 10rem;

  .brand {
    grid-area: brand;

    width: 10rem;
    aspect-ratio: 1 / 1;
  }

  address {
    grid-area: address;
  }

  .other {
    grid-area: other;
  }
}
</style>
