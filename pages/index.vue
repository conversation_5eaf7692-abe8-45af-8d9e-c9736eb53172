<template>
  <div class="hero-wrapper">
    <LayoutContentContainer class="hero">
      <h1 class="tagline">Your Journey to<br>Deeper Discipleship</h1>
      <p class="pitch font-small">The Walk to Emmaus is a transformative program that nurtures Christian discipleship and leadership through an immersive 72-hour course and ongoing support, inspiring participants to actively live out their faith and enhance the vitality of their congregations.</p>
      <video class="background-video" autoplay muted loop playsinline poster="/images/hero-placeholder.jpg">
        <source :src="'/clips/hero-video.mp4'" type="video/mp4">
        <source :src="'/clips/hero-video.webm'" type="video/webm">
        <!-- Fallback for browsers that don't support video -->
        <img src="/images/hero-placeholder.jpg" alt="Walk to Emmaus background" class="background-video">
      </video>
    </LayoutContentContainer>
  </div>

  <LayoutContentContainer>
    <InputFileUploader id="newsletter-uploader"
      entity-id="newsletter"
      :accept="`.pdf,.doc,.docx,.jpg,.jpeg,.png`"
      :max-size="10 * 1024 * 1024">

      <template #default="{ file, url, deleteFile }">
        <a class="file-display" :href="url" target="_blank">
          <section class="file">
            <div class="name">{{ file.name }}</div>
            <div class="description">Download the file</div>
          </section>
          <section class="document">
            <span class="format">{{ file.format }}</span>
            <i class="icon fa-sharp fa-thin fa-file"></i>
          </section>
          <LayoutAdminToolbar class="file-management-tools">
            <button @click="deleteFile" class="remove text-red">
              <i class="fa-sharp fa-regular fa-trash"></i>
            </button>
          </LayoutAdminToolbar>
        </a>
      </template>
    </InputFileUploader>

    <h2>What is the Walk to Emmaus all about?</h2>

    <div class="about-emmaus">
      <article>
        <img src="/images/star.png" alt="Focus on Discipleship" />
        <h4>Focus on Discipleship</h4>
        <p>The Walk to Emmaus is specifically designed to nurture Christian discipleship and leadership among participants.</p>
      </article>

      <article>
        <img src="/images/branch.png" alt="Comprehensive Immersion" />
        <h4>Comprehensive Immersion</h4>
        <p>The initial 72-hour course offers an immersive experience that integrates prayer and acts of sacrificial service, deepening participants' understanding of Christianity.</p>
      </article>

      <article>
        <img src="/images/palm-branch.png" alt="Spiritual Growth" />
        <h4>Spiritual Growth</h4>
        <p>The program is beneficial not only for active church members but also for those seeking to reconnect with their faith, making it accessible to a wider audience.</p>
      </article>

      <article>
        <img src="/images/concentric-circles.png" alt="Community Support" />
        <h4>Community Support</h4>
        <p>After the course, participants engage in follow-up groups, ensuring continued spiritual development and connection with fellow believers.</p>
      </article>

      <article>
        <img src="/images/candle-flame.png" alt="Leadership Development" />
        <h4>Leadership Development</h4>
        <p>The program equips individuals with the skills and confidence needed to become effective Christian leaders within their homes, churches, and communities.</p>
      </article>

      <article>
        <img src="/images/ornate-cross.png" alt="Congregational Vitality" />
        <h4>Congregational Vitality</h4>
        <p>By cultivating disciples and developing leaders, The Walk to Emmaus contributes to the overall health and vitality of the church community.</p>
      </article>
    </div>
  </LayoutContentContainer>
</template>

<script lang="ts" setup>

</script>

<style scoped>
.hero-wrapper {
  block-size: 100dvh;
  padding-inline: 5rlh;
}

.hero {
  position: relative;

  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(4, 1fr);
  grid-template-areas:
    "... ... ... ..."
    "... ... ... ..."
    "tagline tagline tagline tagline"
    "... ... ... pitch";

  block-size: 100cqh;

  .tagline {
    grid-area: tagline;

    color: var(--gold);
    font-size: var(--font-size-lead2);
  }

  .pitch {
    grid-area: pitch;
  }

  .background-video {
    grid-area: 2 / 1 / 4 / 5;
    justify-self: center;
    place-self: center;

    inline-size: 66cqi;
    block-size: auto;
    border-radius: 1rlh;
    transform: translateZ(0); /* Force hardware acceleration */
    -webkit-transform: translateZ(0); /* Safari-specific hardware acceleration */
    z-index: -1;
  }
}

#newsletter-uploader {
  margin-block: 5rlh;
}

.about-emmaus {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rlh;

  article {
    padding: 2rlh;
    text-align: center;
    color: var(--cream);
    border-radius: 1rlh;
  }

  article:nth-child(1) {
    background: var(--dark-green);
    background: linear-gradient(to bottom,
      color-mix(in oklch, var(--dark-green), var(--pure-purple) 5%) 0%,
      var(--dark-green) 100%);
  }

  article:nth-child(2) {
    background: linear-gradient(to bottom,
      color-mix(in oklch, var(--gold), var(--black) 30%) 0%,
      var(--light-green) 100%);
  }

  article:nth-child(3) {
    background: var(--red);
  }

  article:nth-child(4) {
    background: color-mix(in oklch, var(--red), var(--black) 20%);
  }

  article:nth-child(5) {
    background: color-mix(in oklch, var(--gold), var(--black) 30%);
  }

  article:nth-child(6) {
    background: var(--dark-green);
  }

  img {
    block-size: 12rlh;
  }
}

.file-display {
  position: relative;

  display: grid;
  align-items: center;
  grid-template-columns: 1fr min-content;
  grid-template-rows: auto;

  padding: 1lh;
  inline-size: 100%;
  block-size: 5lh;
  background-color: light-dark(oklch(from black l c h / 0.05), oklch(from white l c h / 0.1));
  border: 0;
  border-radius: 0.5rem;

  .name {
    font-weight: bold;
  }

  .document {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
    place-items: center;

    .icon {
      grid-area: 1 / 1;

      font-size: 3lh;
      color: var(--pink);
    }

    .format {
      grid-area: 1 / 1;

      font-size: 0.75rem;
      color: var(--pink);
      font-weight: bolder;
      text-transform: uppercase;
    }
  }

  .file-management-tools {
    position: absolute;
    top: 0.5lh;
    right: 0.5lh;

    opacity: 0;
    transform: translateY(0.5lh);
    transition: opacity 0.2s ease, transform 0.2s ease;
    will-change: opacity, transform;
  }

  .file-display:hover .file-management-tools {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
