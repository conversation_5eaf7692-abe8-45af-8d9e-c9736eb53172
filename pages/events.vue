<template>
  <LayoutContentContainer>
    <ul class="event-list">
      <component v-for="event in chronologicalEvents"
        :is="getComponent(event)"
        v-bind="getDynamicAttributes(event)"
        :key="event.$id">
      </component>
    </ul>

    <LayoutAdminToolbar class="page-management-tools">
      <button id="add-walk-trigger" popovertarget="add-walk-popover">
        <i class="fa-sharp fa-regular fa-person-walking"></i> &nbsp; Add Walk
      </button>
      <button id="add-gathering-trigger" popovertarget="add-gathering-popover">
        <i class="fa-sharp fa-regular fa-fire"></i> &nbsp; Add Gathering
      </button>

      <LazyWalkAdd />
      <LazyGatheringAdd />
    </LayoutAdminToolbar>

  </LayoutContentContainer>
</template>

<script lang="ts" setup>
import { computed, markRaw } from 'vue'

// Walks
import { useWalkStore } from '~/stores/walk'
import { useWalk } from '~/composables/walk'
const walkStore = useWalkStore()
const walk = useWalk()

walk.hydrate()

// Todo: Move to walk composable?
// Sponsorships
import { useSponsorship } from '~/composables/sponsorship'
const sponsorship = useSponsorship()

sponsorship.hydrate()

// Todo: Move to walk composable?
// Pilgrims
import { usePilgrim } from '~/composables/pilgrim'
const pilgrim = usePilgrim()

pilgrim.hydrate()

// Todo: Move to walk composable?
// Staffers
import { useStaffer } from '~/composables/staffer'
const staffer = useStaffer()

staffer.hydrate()

// Todo: Move to walk composable?
// Intercessors
import { useIntercessor } from '~/composables/intercessor'
const intercessor = useIntercessor()

intercessor.hydrate()

// Todo: Move to walk composable?
// Gatherings
import { useGatheringStore } from '~/stores/gathering'
import { useGathering } from '~/composables/gathering'
const gatheringStore = useGatheringStore()
const gathering = useGathering()

gathering.hydrate()

// Events
import { resolveComponent } from 'vue'

const WalkItem = markRaw(resolveComponent('WalkItem') as object)
const GatheringItem = markRaw(resolveComponent('GatheringItem') as object)

const chronologicalEvents = computed(() => {
  const walks = walkStore.activeWalks.map(walk => {
    (walk as any).type = 'walk'
    return walk
  })

  const gatherings = gatheringStore.activeGatherings.map(gathering => {
    (gathering as any).type = 'gathering'
    return gathering
  })

  const allEvents = [...walks, ...gatherings]
  return allEvents.sort((a, b) => new Date(a.start_at).getTime() - new Date(b.start_at).getTime())
})

const getComponent = (event: object) => {
  return (event as any).type === 'walk' ? WalkItem : GatheringItem
}

const getDynamicAttributes = (event: object) => {
  return (event as any).type === 'walk' ? { walk: event } : { gathering: event }
}
</script>

<style scoped>
.content-container {
  container-type: inline-size;

  margin-top: 33dvb;
}

.event-list {
  display: block flex;
  flex-direction: column;

  list-style-type: none;
  padding: 0;
  margin: 0;
  margin-inline-start: 25cqi;
}

.page-management-tools {
  position: fixed;
  bottom: 2lh;
  right: 2lh;
}
</style>
