<template>
  <LayoutContentContainer>
    <InputFileUploader
      entity-id="board-meeting-minutes"
      :accept="`.pdf,.doc,.docx,.jpg,.jpeg,.png`"
      :max-size="10 * 1024 * 1024">

      <template #default="{ file, url, deleteFile }">
        <a class="file-display" :href="url" target="_blank">
          <section class="file">
            <div class="name">{{ file.name }}</div>
            <div class="description">Download the file</div>
          </section>
          <section class="document">
            <span class="format">{{ file.format }}</span>
            <i class="icon fa-sharp fa-thin fa-file"></i>
          </section>
          <LayoutAdminToolbar class="file-management-tools">
            <button @click="deleteFile" class="remove text-red">
              <i class="fa-sharp fa-regular fa-trash"></i>
            </button>
          </LayoutAdminToolbar>
        </a>
      </template>

    </InputFileUploader>

    <h3>Officers</h3>

    <ul class="officer-list">
      <BoardMemberCard
        v-for="member in officers"
        :member="member"
        :key="member.$id" />
    </ul>

    <h3>Board Members</h3>
    <ul class="officer-list">
      <BoardMemberCard
        v-for="member in members"
        :member="member"
        :key="member.$id" />
    </ul>

    <LayoutAdminToolbar class="page-management-tools">
      <button id="add-boardmember-trigger" popovertarget="add-boardmember-popover">
        <i class="fa-sharp fa-regular fa-user-plus"></i> &nbsp; Add member
      </button>

      <LazyBoardMemberAdd />
    </LayoutAdminToolbar>

  </LayoutContentContainer>
</template>

<script lang="ts" setup>
import { useBoardMemberStore } from '~/stores/boardMember'
const boardMemberStore = useBoardMemberStore()

import { useBoardMember } from '~/composables/boardMember'
const boardMember = useBoardMember()

boardMember.hydrate()

import { computed } from 'vue'
import type { BoardMemberInterface } from '~/utils/appwrite'

const officers = computed(() => {
  return boardMemberStore.members
    .filter((member: BoardMemberInterface) => member.position === 'Officer')
    .sort((a, b) => a.name.localeCompare(b.name))
})

const members = computed(() => {
  return boardMemberStore.members
    .filter((member: BoardMemberInterface) => member.position === 'Board member')
    .sort((a, b) => a.name.localeCompare(b.name))
})
</script>

<style scoped>
.content-container {
  position: relative;

  container-type: inline-size;

  margin-block-start: 33dvb;
  padding-inline-start: 25cqi;
}

.officer-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.page-management-tools {
  position: fixed;
  bottom: 2lh;
  right: 2lh;
}

.file-display {
  position: relative;

  display: grid;
  align-items: center;
  grid-template-columns: 1fr min-content;
  grid-template-rows: auto;

  padding: 1lh;
  inline-size: 100%;
  block-size: 5lh;
  background-color: light-dark(oklch(from black l c h / 0.05), oklch(from white l c h / 0.1));
  border: 0;
  border-radius: 0.5rem;

  .name {
    font-weight: bold;
  }

  .document {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
    place-items: center;

    .icon {
      grid-area: 1 / 1;

      font-size: 3lh;
      color: var(--pink);
    }

    .format {
      grid-area: 1 / 1;

      font-size: 0.75rem;
      color: var(--pink);
      font-weight: bolder;
      text-transform: uppercase;
    }
  }

  .file-management-tools {
    position: absolute;
    top: 0.5lh;
    right: 0.5lh;

    opacity: 0;
    transform: translateY(0.5lh);
    transition: opacity 0.2s ease, transform 0.2s ease;
    will-change: opacity, transform;
  }

  .file-display:hover .file-management-tools {
    opacity: 1;
    transform: translateY(0);
  }
}

.file-actions {
  display: flex;
  gap: 0.5rem;
}
</style>
