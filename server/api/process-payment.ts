import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'h3';
import { readBody } from 'h3';
import crypto from 'crypto';

export default defineEventHandler(async (event) => {
  const body = await readBody(event);

  const baseUrl = process.env.SQUARE_SANDBOX === 'true'
    ? 'https://connect.squareupsandbox.com'
    : 'https://connect.squareup.com';

  const payload = {
    source_id: body.token,
    location_id: process.env.SQUARE_LOCATION_ID,
    idempotency_key: crypto.randomUUID(),
    amount_money: { amount: body.amount * 100, currency: 'USD' },
    autocomplete: true,
    note: 'Walk #123: <PERSON> and <PERSON>do',
  };

  const response = await fetch(`${baseUrl}/v2/payments`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.SQUARE_ACCESS_TOKEN}`,
    },
    body: JSON.stringify(payload),
  });

  return await response.json();
});
