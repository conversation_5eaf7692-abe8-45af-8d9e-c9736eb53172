import { defineStore } from 'pinia'
import { account } from '~/utils/appwrite'

export interface UserState {
  user: any | null
  loading: boolean
  error: string | null
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    user: null,
    loading: false,
    error: null
  }),

  actions: {
    async getCurrentUser () {
      try {
        this.loading = true
        this.error = null
        const user = await account.get()
        this.user = user

        return user
      } catch (error) {
        this.user = null
        return null
      } finally {
        this.loading = false
      }
    },

    async login (email: string, password: string) {
      try {
        this.loading = true
        this.error = null
        const session = await account.createEmailPasswordSession(email, password)
        await this.getCurrentUser()

        return session
      } catch (error: any) {
        this.error = error.message || 'Failed to login'
        throw error
      } finally {
        this.loading = false
      }
    },

    async logout () {
      try {
        this.loading = true
        this.error = null
        await account.deleteSession('current')
        this.user = null
      } catch (error: any) {
        this.error = error.message || 'Failed to logout'
        throw error
      } finally {
        this.loading = false
      }
    }
  }
})
