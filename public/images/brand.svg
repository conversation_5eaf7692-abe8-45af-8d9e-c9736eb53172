<svg viewBox="0 0 1109 1109" xmlns="http://www.w3.org/2000/svg" xml:space="preserve" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:1.5">
  <g transform="scale(1.1089)">
    <path style="fill:none" d="M0 0h1000v1000H0z"/>
    <clipPath id="a">
      <path d="M0 0h1000v1000H0z"/>
    </clipPath>
    <g clip-path="url(#a)">
      <path d="M499.99-.003c275.963 0 500 224.036 500 499.998 0 275.95-224.037 499.998-500 499.998-275.95 0-499.998-224.048-499.998-499.998C-.008 224.033 224.041-.003 499.991-.003Zm0 3.603C226.023 3.6 3.608 226.026 3.608 499.995c0 273.97 222.415 496.395 496.384 496.395 273.969 0 496.395-222.426 496.395-496.395C996.386 226.025 773.96 3.6 499.991 3.6Z"/>
      <path d="M.418.005a.334.334 0 0 0 .249-.098L.635-.126a.294.294 0 0 1-.215.084.304.304 0 0 1-.314-.308c0-.176.135-.308.314-.308.082 0 .155.024.215.083l.032-.033a.334.334 0 0 0-.249-.097.35.35 0 0 0-.363.355.35.35 0 0 0 .363.355Z" style="fill-rule:nonzero" transform="scale(86.57233) rotate(77.212 -3.686 3.776)"/>
      <path d="M.641-.7v.322H.173V-.7H.122V0h.051v-.333h.468V0h.051v-.7H.641Z" style="fill-rule:nonzero" transform="rotate(68.443 -418.575 374.65) scale(86.5723)"/>
      <path d="M.666 0 .491-.245a.215.215 0 0 0 .164-.219C.655-.612.549-.7.373-.7H.122V0h.051v-.229h.2a.494.494 0 0 0 .068-.005L.608 0h.058ZM.173-.274v-.38h.2c.15 0 .23.069.23.19 0 .12-.08.19-.23.19h-.2Z" style="fill-rule:nonzero" transform="rotate(59.245 -553.588 442.434) scale(86.57234)"/>
      <path d="M.62-.7H.567l-.253.412L.061-.7H.005l.282.459V0h.051v-.241L.62-.7Z" style="fill-rule:nonzero" transform="scale(86.5723) rotate(52.067 -7.991 5.972)"/>
      <path d="M.306.005c.173 0 .252-.086.252-.185 0-.254-.437-.13-.437-.34 0-.077.061-.14.197-.14.063 0 .134.019.192.059l.019-.041a.376.376 0 0 0-.211-.063c-.172 0-.248.087-.248.186 0 .258.437.132.437.342 0 .076-.062.137-.202.137a.333.333 0 0 1-.231-.088L.051-.09a.359.359 0 0 0 .255.095Z" style="fill-rule:nonzero" transform="scale(86.57234) rotate(45.235 -9.9 6.95)"/>
      <path d="M.645 0H.7L.378-.7H.327L.005 0H.06l.089-.197h.406L.645 0ZM.169-.24l.183-.404.184.404H.169Z" style="fill-rule:nonzero" transform="rotate(37.762 -1099.65 720.177) scale(86.5723)"/>
      <path d="M.122 0h.454v-.046H.173V-.7H.122V0Z" style="fill-rule:nonzero" transform="rotate(29.76 -1491.897 918.264) scale(86.57234)"/>
      <path style="fill-rule:nonzero" d="M.122-.7h.051V0H.122z" transform="rotate(25.348 -1819.543 1117.491) scale(86.57231)"/>
      <path d="M.306.005c.173 0 .252-.086.252-.185 0-.254-.437-.13-.437-.34 0-.077.061-.14.197-.14.063 0 .134.019.192.059l.019-.041a.376.376 0 0 0-.211-.063c-.172 0-.248.087-.248.186 0 .258.437.132.437.342 0 .076-.062.137-.202.137a.333.333 0 0 1-.231-.088L.051-.09a.359.359 0 0 0 .255.095Z" style="fill-rule:nonzero" transform="scale(86.57238) rotate(20.147 -27.314 15.788)"/>
      <path d="M.622-.106a.306.306 0 0 1-.199.064.305.305 0 0 1-.317-.308c0-.176.135-.308.317-.308.085 0 .158.023.219.083l.032-.034a.342.342 0 0 0-.252-.096c-.212 0-.367.151-.367.355S.21.005.421.005a.36.36 0 0 0 .25-.09v-.262H.622v.241Z" style="fill-rule:nonzero" transform="scale(86.57234) rotate(-2.202 283.558 -140.44)"/>
      <path d="M.419.005A.35.35 0 0 0 .783-.35a.35.35 0 0 0-.364-.355.35.35 0 0 0-.364.355.35.35 0 0 0 .364.355Zm0-.047A.303.303 0 0 1 .106-.35c0-.177.134-.308.313-.308.178 0 .312.131.312.308a.302.302 0 0 1-.312.308Z" style="fill-rule:nonzero" transform="rotate(-11.644 4845.967 -2242.203) scale(86.57228)"/>
      <path style="fill-rule:nonzero" d="M.122-.7h.051V0H.122z" transform="rotate(-18.179 3174.234 -1489.241) scale(86.57231)"/>
      <path d="M.641-.7v.608L.165-.7H.122V0h.051v-.608L.65 0h.042v-.7H.641Z" style="fill-rule:nonzero" transform="rotate(-24.562 2412.183 -1033.319) scale(86.5723)"/>
      <path d="M.622-.106a.306.306 0 0 1-.199.064.305.305 0 0 1-.317-.308c0-.176.135-.308.317-.308.085 0 .158.023.219.083l.032-.034a.342.342 0 0 0-.252-.096c-.212 0-.367.151-.367.355S.21.005.421.005a.36.36 0 0 0 .25-.09v-.262H.622v.241Z" style="fill-rule:nonzero" transform="rotate(-33.407 1821.49 -746.433) scale(86.5723)"/>
      <path d="M.59-.654V-.7H.122V0h.051v-.309h.373v-.046H.173v-.299H.59Z" style="fill-rule:nonzero" transform="scale(86.57234) rotate(-45.043 16.068 -6.15)"/>
      <path d="M.419.005A.35.35 0 0 0 .783-.35a.35.35 0 0 0-.364-.355.35.35 0 0 0-.364.355.35.35 0 0 0 .364.355Zm0-.047A.303.303 0 0 1 .106-.35c0-.177.134-.308.313-.308.178 0 .312.131.312.308a.302.302 0 0 1-.312.308Z" style="fill-rule:nonzero" transform="scale(86.5723) rotate(-52.816 13.952 -5.014)"/>
      <path d="M.666 0 .491-.245a.215.215 0 0 0 .164-.219C.655-.612.549-.7.373-.7H.122V0h.051v-.229h.2a.494.494 0 0 0 .068-.005L.608 0h.058ZM.173-.274v-.38h.2c.15 0 .23.069.23.19 0 .12-.08.19-.23.19h-.2Z" style="fill-rule:nonzero" transform="scale(86.5723) rotate(-62.104 12.037 -4.09)"/>
      <path d="M.256 0h.051v-.654h.252V-.7H.004v.046h.252V0Z" style="fill-rule:nonzero" transform="rotate(-68.971 941.634 -313.204) scale(86.57233)"/>
      <path d="M.641-.7v.322H.173V-.7H.122V0h.051v-.333h.468V0h.051v-.7H.641Z" style="fill-rule:nonzero" transform="rotate(-76.921 855.421 -259.217) scale(86.57237)"/>
      <path d="M.834 0 .833-.7H.791l-.313.541L.165-.7H.122V0h.05v-.597l.293.504H.49l.293-.506L.784 0h.05Z" style="fill-rule:nonzero" transform="rotate(-78.243 350.401 152.958) scale(86.5723)"/>
      <path style="fill-rule:nonzero" d="M.122-.7h.051V0H.122z" transform="rotate(-69.809 349.538 86.04) scale(86.57237)"/>
      <path d="M.122 0h.277c.222 0 .372-.146.372-.35S.621-.7.399-.7H.122V0Zm.051-.046v-.608h.222c.198 0 .325.126.325.304s-.127.304-.325.304H.173Z" style="fill-rule:nonzero" transform="rotate(-61.8 378.083 49.994) scale(86.57237)"/>
      <path d="M.834 0 .833-.7H.791l-.313.541L.165-.7H.122V0h.05v-.597l.293.504H.49l.293-.506L.784 0h.05Z" style="fill-rule:nonzero" transform="rotate(-46.919 420.518 -93.282) scale(86.57237)"/>
      <path style="fill-rule:nonzero" d="M.122-.7h.051V0H.122z" transform="rotate(-38.596 441.85 -261.14) scale(86.5723)"/>
      <path d="M.418.005a.334.334 0 0 0 .249-.098L.635-.126a.294.294 0 0 1-.215.084.304.304 0 0 1-.314-.308c0-.176.135-.308.314-.308.082 0 .155.024.215.083l.032-.033a.334.334 0 0 0-.249-.097.35.35 0 0 0-.363.355.35.35 0 0 0 .363.355Z" style="fill-rule:nonzero" transform="scale(86.57232) rotate(-31.791 5.7 -4.491)"/>
      <path d="M.641-.7v.322H.173V-.7H.122V0h.051v-.333h.468V0h.051v-.7H.641Z" style="fill-rule:nonzero" transform="rotate(-21.557 608.583 -786.24) scale(86.5723)"/>
      <path style="fill-rule:nonzero" d="M.122-.7h.051V0H.122z" transform="rotate(-14.063 782.9 -1539.481) scale(86.5723)"/>
      <path d="M.622-.106a.306.306 0 0 1-.199.064.305.305 0 0 1-.317-.308c0-.176.135-.308.317-.308.085 0 .158.023.219.083l.032-.034a.342.342 0 0 0-.252-.096c-.212 0-.367.151-.367.355S.21.005.421.005a.36.36 0 0 0 .25-.09v-.262H.622v.241Z" style="fill-rule:nonzero" transform="scale(86.57233) rotate(-7.163 15.059 -38.166)"/>
      <path d="M.645 0H.7L.378-.7H.327L.005 0H.06l.089-.197h.406L.645 0ZM.169-.24l.183-.404.184.404H.169Z" style="fill-rule:nonzero" transform="scale(86.5723) rotate(2.929 -26.293 111.162)"/>
      <path d="M.641-.7v.608L.165-.7H.122V0h.051v-.608L.65 0h.042v-.7H.641Z" style="fill-rule:nonzero" transform="rotate(13.124 -296.975 2456.162) scale(86.57236)"/>
      <path d="M.173-.046v-.287h.373v-.045H.173v-.276H.59V-.7H.122V0h.483v-.046H.173Z" style="fill-rule:nonzero" transform="scale(86.57235) rotate(27.004 -.053 16.284)"/>
      <path d="M.834 0 .833-.7H.791l-.313.541L.165-.7H.122V0h.05v-.597l.293.504H.49l.293-.506L.784 0h.05Z" style="fill-rule:nonzero" transform="rotate(37.484 79.314 1110.357) scale(86.57233)"/>
      <path d="M.834 0 .833-.7H.791l-.313.541L.165-.7H.122V0h.05v-.597l.293.504H.49l.293-.506L.784 0h.05Z" style="fill-rule:nonzero" transform="scale(86.57231) rotate(50.207 1.523 10.681)"/>
      <path d="M.645 0H.7L.378-.7H.327L.005 0H.06l.089-.197h.406L.645 0ZM.169-.24l.183-.404.184.404H.169Z" style="fill-rule:nonzero" transform="scale(86.57235) rotate(61.301 1.795 9.605)"/>
      <path d="M.396.005c.174 0 .281-.102.281-.302V-.7H.626v.401c0 .175-.083.257-.229.257-.146 0-.23-.082-.23-.257V-.7H.116v.403c0 .2.106.302.28.302Z" style="fill-rule:nonzero" transform="rotate(71.201 176.417 760.364) scale(86.57234)"/>
      <path d="M.306.005c.173 0 .252-.086.252-.185 0-.254-.437-.13-.437-.34 0-.077.061-.14.197-.14.063 0 .134.019.192.059l.019-.041a.376.376 0 0 0-.211-.063c-.172 0-.248.087-.248.186 0 .258.437.132.437.342 0 .076-.062.137-.202.137a.333.333 0 0 1-.231-.088L.051-.09a.359.359 0 0 0 .255.095Z" style="fill-rule:nonzero" transform="scale(86.57233) rotate(80.671 2.159 8.245)"/>
      <path d="M499.999 190.979c170.557 0 309.023 138.465 309.023 309.023 0 170.55-138.466 309.023-309.023 309.023-170.55 0-309.023-138.473-309.023-309.023 0-170.558 138.472-309.023 309.023-309.023Zm0 3.608c-168.56 0-305.414 136.848-305.414 305.415 0 168.56 136.854 305.414 305.414 305.414 168.567 0 305.421-136.854 305.421-305.414 0-168.567-136.854-305.415-305.421-305.415Z"/>
      <clipPath id="b">
        <path d="M372.944 646.415c-1.588 11.913-8.188 20.095-21.095 23.585h72.218l-.138-74.966 50.727 5.711-.139 45.783c-1.462 11.188-7.729 19.226-19.781 23.472h70.651l.123-63.531 16.507 1.858 33.917-11.285.076 49.421c-.981 10.036-6.798 17.924-17.736 23.537h68.712l.095-76.095c17.118-.434 25.145-9.195 25.109-25.342l74.128 11.533 81.081-49.321C791.622 656.648 680.489 809.921 500 809.024c-157.797-.785-271.113-117.644-302.777-247.418l52.512-41.605 123.619 65.364-.41 61.05Z"/>
      </clipPath>
      <g clip-path="url(#b)">
        <path d="M97.569 675.283h888.992" style="fill:none;stroke:#000;stroke-width:3.54px" transform="matrix(1.12487 0 0 .9018 -109.751 61.033)"/>
        <path d="M97.569 675.283h888.992" style="fill:none;stroke:#000;stroke-width:5.69px" transform="matrix(.67915 0 0 .585 329.98 302.725)"/>
        <path d="M97.569 675.283h888.992" style="fill:none;stroke:#000;stroke-width:12.83px" transform="matrix(.31209 0 0 .24618 65.125 587.059)"/>
        <path d="M97.569 675.283h888.992" style="fill:none;stroke:#000;stroke-width:12.83px" transform="matrix(.31209 0 0 .24618 387.797 587.059)"/>
        <path d="M97.569 675.283h888.992" style="fill:none;stroke:#000;stroke-width:12.83px" transform="matrix(.31209 0 0 .24618 511.187 614.826)"/>
        <path d="M97.569 675.283h888.992" style="fill:none;stroke:#000;stroke-width:12.83px" transform="matrix(.31209 0 0 .24618 187.96 614.826)"/>
        <path d="M97.569 675.283h888.992" style="fill:none;stroke:#000;stroke-width:12.83px" transform="matrix(.31209 0 0 .24618 43.242 531.524)"/>
        <path d="M97.569 675.283h888.992" style="fill:none;stroke:#000;stroke-width:41.75px" transform="matrix(.0916 0 0 .08085 467.927 670.94)"/>
        <path d="M97.569 675.283H1893.7" style="fill:none;stroke:#000;stroke-width:15.9px" transform="matrix(.27837 0 0 .1597 576.703 617.691)"/>
        <path d="M97.569 675.283H1893.7" style="fill:none;stroke:#000;stroke-width:15.9px" transform="matrix(.27837 0 0 .1597 -99.148 617.691)"/>
        <path d="m211.774 631.186 65.313-51.951 146.774 77.62 177.498 19.924 120.139-40.02 84.289 8.826 101.284-61.488" style="fill:none;stroke:#000;stroke-width:4px" transform="scale(.9018)"/>
        <path d="m211.774 632.043 91.602-33.046 45.429 43.549 94.899 18.553 115.718-14.686 102.294 23.646 77.543-36.504 174.397-23.746" style="fill:none;stroke:#000;stroke-width:4px" transform="matrix(.9018 0 0 .9018 0 43.982)"/>
      </g>
      <g transform="translate(0 -170)">
        <clipPath id="c">
          <path d="M500 360.976c170.555 0 309.024 138.469 309.024 309.024 0 13.244-2.397 34.226-2.397 34.226l-80.31 47.899-78.446-17.595-8.998 3.943-12.108.128-.265-50.791h-28.858c-12.737 3.376-19.806 11.263-21.742 23.237l.327 57.944-34.225 11.324-16.507-1.808-.262-14.602c17.86-.117 25.632-9.155 25.343-25.32l-25.222-.158-.133-50.617H497.73c-12.651 1.193-20.369 8.737-22.953 22.89l-.067 61.957-50.74-5.774-.091-2.978c17.011.405 25.04-8.511 25.421-25.365l-25.458.094-.231-50.824h-30.44c-13.317 4.325-19.932 13.004-20.119 25.855l-.009 43.733-123.301-65.047s-51.915 42.606-52.061 41.917c-4.393-20.734-6.704-42.233-6.704-64.268 0-170.555 138.468-309.024 309.023-309.024Z"/>
        </clipPath>
        <g clip-path="url(#c)">
          <path d="M554.45 0v504.45m0 100v504.45" style="fill:none;stroke:#000;stroke-width:4px" transform="scale(.9018)"/>
          <path d="M554.45 0v504.441m0 100.019v504.44" style="fill:none;stroke:#000;stroke-width:4px" transform="scale(.9018) rotate(-15 554.45 554.448)"/>
          <path d="M554.45 0v504.449m0 100.002V1108.9" style="fill:none;stroke:#000;stroke-width:4px" transform="scale(.9018) rotate(-30 554.45 554.45)"/>
          <path d="M554.45 0v504.46m0 99.98v504.46" style="fill:none;stroke:#000;stroke-width:4px" transform="scale(.9018) rotate(-45 554.45 554.45)"/>
          <path d="M554.45 0v504.449m0 100.002V1108.9" style="fill:none;stroke:#000;stroke-width:4px" transform="scale(.9018) rotate(-60 554.45 554.45)"/>
          <path d="M554.45 0v504.44m0 100.019V1108.9" style="fill:none;stroke:#000;stroke-width:4px" transform="scale(.9018) rotate(-75 554.45 554.45)"/>
          <path d="M554.45 0v504.45m0 100v504.45" style="fill:none;stroke:#000;stroke-width:4px" transform="matrix(0 -.9018 .9018 0 0 1000)"/>
          <path d="M554.45 0v504.44m0 100.019V1108.9" style="fill:none;stroke:#000;stroke-width:4px" transform="scale(-.9018) rotate(75 722.571 -722.572)"/>
          <path d="M554.45 0v504.449m0 100.001v504.45" style="fill:none;stroke:#000;stroke-width:4px" transform="scale(-.9018) rotate(60 960.332 -960.335)"/>
          <path d="M554.45 0v504.46m0 99.98v504.46" style="fill:none;stroke:#000;stroke-width:4px" transform="scale(-.9018) rotate(45 1338.565 -1338.563)"/>
          <path d="M554.45 0v504.449m0 100.001v504.45" style="fill:none;stroke:#000;stroke-width:4px" transform="scale(-.9018) rotate(30 2069.231 -2069.236)"/>
          <path d="M554.45 0v504.44m0 100.019V1108.9" style="fill:none;stroke:#000;stroke-width:4px" transform="scale(-.9018) rotate(15 4211.447 -4211.46)"/>
        </g>
      </g>
      <g transform="matrix(.5073 0 0 .5073 -543.515 -429.34)">
        <path d="M199.293 411.264H69.819c23.82 0 43.158-19.338 43.158-43.158V195.474c0-23.82 19.339-43.158 43.158-43.158h43.158v258.948Z" style="fill:none;stroke:#000;stroke-width:6.14px" transform="translate(1676.11 1690.58) scale(1.15853)"/>
        <path d="M1907 1967.05v12.11a87.833 87.833 0 0 1-87.82 87.83h-12.12v-99.94H1907Z" style="fill:none;stroke:#000;stroke-width:14.21px" transform="matrix(.5003 0 0 .5003 1002.92 982.922)"/>
        <clipPath id="d">
          <path d="M1907 1967.05v-107.42h54.96v307.42H1907v-150h6.06a43.93 43.93 0 0 0 31.07-12.87 43.947 43.947 0 0 0 12.87-31.07v-6.06h-50Z"/>
        </clipPath>
        <g clip-path="url(#d)">
          <path d="m1941.62 1867.05-10 31.81v268.19" style="fill:none;stroke:#000;stroke-width:7.11px"/>
        </g>
      </g>
      <g transform="matrix(.5073 0 0 .5073 -442.055 -429.34)">
        <path d="M199.293 411.264H69.819c23.82 0 43.158-19.338 43.158-43.158V195.474c0-23.82 19.339-43.158 43.158-43.158h43.158v258.948Z" style="fill:none;stroke:#000;stroke-width:6.14px" transform="translate(1676.11 1690.58) scale(1.15853)"/>
        <path d="M1907 1967.05v12.11a87.833 87.833 0 0 1-87.82 87.83h-12.12v-99.94H1907Z" style="fill:none;stroke:#000;stroke-width:14.21px" transform="matrix(.5003 0 0 .5003 1002.92 982.922)"/>
        <clipPath id="e">
          <path d="M1907 1967.05v-107.42h54.96v307.42H1907v-150h6.06a43.93 43.93 0 0 0 31.07-12.87 43.947 43.947 0 0 0 12.87-31.07v-6.06h-50Z"/>
        </clipPath>
        <g clip-path="url(#e)">
          <path d="m1941.62 1867.05-10 31.81v268.19" style="fill:none;stroke:#000;stroke-width:7.11px"/>
        </g>
      </g>
      <g transform="matrix(.5073 0 0 .5073 -340.596 -429.34)">
        <path d="M199.293 411.264H69.819c23.82 0 43.158-19.338 43.158-43.158V195.474c0-23.82 19.339-43.158 43.158-43.158h43.158v258.948Z" style="fill:none;stroke:#000;stroke-width:6.14px" transform="translate(1676.11 1690.58) scale(1.15853)"/>
        <path d="M1907 1967.05v12.11a87.833 87.833 0 0 1-87.82 87.83h-12.12v-99.94H1907Z" style="fill:none;stroke:#000;stroke-width:14.21px" transform="matrix(.5003 0 0 .5003 1002.92 982.922)"/>
        <clipPath id="f">
          <path d="M1907 1967.05v-107.42h54.96v307.42H1907v-150h6.06a43.93 43.93 0 0 0 31.07-12.87 43.947 43.947 0 0 0 12.87-31.07v-6.06h-50Z"/>
        </clipPath>
        <g clip-path="url(#f)">
          <path d="m1941.62 1867.05-10 31.81v268.19" style="fill:none;stroke:#000;stroke-width:7.11px"/>
        </g>
      </g>
    </g>
  </g>
  <path d="M-554.444-1108.886c306.016 0 554.451 248.435 554.451 554.45C.007-248.433-248.428.015-554.443.015c-306.003 0-554.451-248.448-554.451-554.45 0-306.016 248.448-554.451 554.45-554.451Zm0 3.995c-303.806 0-550.442 246.65-550.442 550.455 0 303.806 246.636 550.456 550.442 550.456 303.806 0 550.456-246.65 550.456-550.456 0-303.805-246.65-550.455-550.456-550.455Z"/>
</svg>
