import * as XLSX from 'xlsx';

export class XlsxExporter {
  // Format a date as mm/dd/yyyy hh:mm:ss AM/PM
  private static formatDate(date: Date | string): string {
    const dateObj = date instanceof Date ? date : new Date(date);

    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    const year = dateObj.getFullYear();

    // Get hours in 12-hour format
    let hours = dateObj.getHours();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'

    const hoursStr = String(hours).padStart(2, '0');
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');
    const seconds = String(dateObj.getSeconds()).padStart(2, '0');

    return `${month}/${day}/${year} ${hoursStr}:${minutes}:${seconds} ${ampm}`;
  }

  // Convert snake_case to Initial Caps
  private static formatKey(key: string): string {
    return key
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  // Process a value based on its type
  private static processValue(value: any): any {
    // Handle Date objects or date strings
    if (value instanceof Date || (typeof value === 'string' && !isNaN(Date.parse(value)))) {
      return XlsxExporter.formatDate(value);
    }

    // Handle potential JSON strings
    if (typeof value === 'string') {
      try {
        const parsedValue = JSON.parse(value);
        if (typeof parsedValue === 'object' && parsedValue !== null) {
          // Convert object to string of values
          return Object.values(parsedValue).filter(v => v).join(' ');
        }
        return value;
      } catch (e) {
        // Not a valid JSON string, keep as is
        return value;
      }
    }

    // Return other values as is
    return value;
  }

  // Clean data by removing system fields and formatting
  private static cleanData(items: any[]): any[] {
    return items.map(item => {
      const { $id, $createdAt, $updatedAt, $permissions, $databaseId, $collectionId, walk_id, ...cleanItem } = item;

      // Create a new object with formatted keys and processed values
      const formattedItem: Record<string, any> = {};

      Object.keys(cleanItem).forEach(key => {
        const formattedKey = XlsxExporter.formatKey(key);
        formattedItem[formattedKey] = XlsxExporter.processValue(cleanItem[key]);
      });

      return formattedItem;
    });
  }

  static exportWalk(walkId: string, walkData: any, relatedData: {
    pilgrims?: any[],
    sponsorships?: any[],
    staffers?: any[],
    intercessors?: any[]
  }): void {
    if (!walkData) return;

    // Create workbook with multiple sheets
    const wb = XLSX.utils.book_new();

    // Pilgrims sheet
    if (relatedData.pilgrims && relatedData.pilgrims.length > 0) {
      const pilgrimSheet = XLSX.utils.json_to_sheet(XlsxExporter.cleanData(relatedData.pilgrims));
      XLSX.utils.book_append_sheet(wb, pilgrimSheet, 'Pilgrims');
    }

    // Sponsorships sheet
    if (relatedData.sponsorships && relatedData.sponsorships.length > 0) {
      const sponsorshipSheet = XLSX.utils.json_to_sheet(XlsxExporter.cleanData(relatedData.sponsorships));
      XLSX.utils.book_append_sheet(wb, sponsorshipSheet, 'Sponsorships');
    }

    // Staffers sheet
    if (relatedData.staffers && relatedData.staffers.length > 0) {
      const stafferSheet = XLSX.utils.json_to_sheet(XlsxExporter.cleanData(relatedData.staffers));
      XLSX.utils.book_append_sheet(wb, stafferSheet, 'Staffers');
    }

    // Intercessors sheet
    if (relatedData.intercessors && relatedData.intercessors.length > 0) {
      const intercessorSheet = XLSX.utils.json_to_sheet(XlsxExporter.cleanData(relatedData.intercessors));
      XLSX.utils.book_append_sheet(wb, intercessorSheet, 'Intercessors');
    }

    // Export the file
    XLSX.writeFile(wb, `${walkData.name.replace(/\s+/g, '_')}_Walk_${walkData.number}.xlsx`);
  }
}
