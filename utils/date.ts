/**
 * Date utility functions for handling conversions between local time and UTC
 * for use with Appwrite and UI components
 */

/**
 * Converts a UTC date to local time for display in the UI
 * @param utcDate Date object or ISO string in UTC
 * @returns Date object in local time
 */
export function toLocalTime(utcDate: Date | string | null): Date | null {
  if (!utcDate) return null;

  const date = utcDate instanceof Date ? utcDate : new Date(utcDate);
  return date;
  // Note: JavaScript Date objects automatically handle timezone conversion
  // when displayed or when using methods like toLocaleString()
}

/**
 * Formats a date for datetime-local input fields
 * @param date Date object or ISO string
 * @returns Formatted string in the format YYYY-MM-DDTHH:MM
 */
export function formatForDateTimeInput(date: Date | string | null): string {
  if (!date) return '';

  const localDate = date instanceof Date ? date : new Date(date);
  if (!localDate) return '';

  // Format as YYYY-MM-DDTHH:MM (required format for datetime-local inputs)
  // Use local timezone values instead of UTC
  const year = localDate.getFullYear();
  const month = String(localDate.getMonth() + 1).padStart(2, '0');
  const day = String(localDate.getDate()).padStart(2, '0');
  const hours = String(localDate.getHours()).padStart(2, '0');
  const minutes = String(localDate.getMinutes()).padStart(2, '0');

  return `${year}-${month}-${day}T${hours}:${minutes}`;
}

/**
 * Converts a local date to UTC for storing in Appwrite
 * @param localDate Date object in local time
 * @returns Date object in UTC
 */
export function toUTC(localDate: Date | string | null): Date | null {
  if (!localDate) return null;

  const date = localDate instanceof Date ? localDate : new Date(localDate);
  return date;
  // JavaScript Date objects are already in UTC internally
}

/**
 * Prepares a date object for sending to Appwrite
 * @param date Date object or string
 * @returns ISO string in UTC format
 */
export function prepareForAppwrite(date: Date | string | null): string | null {
  if (!date) return null;

  const utcDate = toUTC(date);
  return utcDate ? utcDate.toISOString() : null;
}

/**
 * Parses a date from an input field and converts to a Date object
 * @param inputValue Value from datetime-local input
 * @returns Date object
 */
export function parseFromInput(inputValue: string): Date | null {
  if (!inputValue) return null;

  // Create a date object from the input value
  // The browser already handles the timezone for datetime-local inputs
  return new Date(inputValue);
}

/**
 * Adds hours to a date
 * @param date The base date
 * @param hours Number of hours to add
 * @returns New date with hours added
 */
export function addHours(date: Date | null, hours: number): Date | null {
  if (!date) return null;

  const newDate = new Date(date);
  newDate.setHours(newDate.getHours() + hours);
  return newDate;
}
