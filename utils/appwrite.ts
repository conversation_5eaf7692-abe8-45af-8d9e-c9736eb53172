import { ID, Client, Account, Databases, Storage } from 'appwrite'

const appwrite_endpoint: string    = 'https://cloud.appwrite.io/v1'
const appwrite_project_id: string  = '6716ec1c0002987f447d'
const appwrite_database_id: string = '671aa084003da6dde4bb'
const appwrite_bucket_id: string   = '6732636200228f3a717f'

export const client = new Client();

client
  .setEndpoint(appwrite_endpoint)
  .setProject(appwrite_project_id);

export const databases = new Databases(client);
export const storage = new Storage(client);

interface BaseCollectionInterface {
  $id          : string
  $createdAt   : datetime | null
  $updatedAt   : datetime | null
  $permissions : array
  $collectionId: string
}

class BaseCollection implements BaseCollectionInterface {
  $id          : string   = ''
  $createdAt   : datetime = null
  $updatedAt   : datetime = null
  $permissions : array    = []
  $collectionId: string   = ''

  get = () => {
    try {
      return databases.listDocuments(
        appwrite_database_id,
        this.$collectionId,
      ).then(response => response.documents)
    } catch (error) {
      console.error(error)
    }
  }

  update = (id: string, data: any) => {
    try {
      const { $id, $createdAt, $updatedAt, $permissions, $databaseId, $collectionId, ...cleanData } = data;

      return databases.updateDocument(
        appwrite_database_id,
        this.$collectionId,
        id,
        prepareDataForRequest(cleanData),
      );
    } catch (error) {
      console.error(error);
    }
  }

  create = (data: any) => {
    try {
      return databases.createDocument(
        appwrite_database_id,
        this.$collectionId,
        ID.unique(),
        prepareDataForRequest(data),
      );
    } catch (error) {
      console.error(error);
    }
  }

  delete = (id: string) => {
    try {
      return databases.deleteDocument(
        appwrite_database_id,
        this.$collectionId,
        id,
      );
    } catch (error) {
      console.error(error);
    }
  }
}

// * Walks
export interface WalkInterface {
  number          : string;
  name            : string;
  address         : object | null;
  start_at        : Date | null;
  end_at          : Date | null;
  notes           : string;
  max_participants: number;
  is_active       : boolean;
  price           : number;
  deposit         : number;
  staffer_price   : number;
};

export class WalkCollection extends BaseCollection implements WalkInterface {
  number           : string        = '';
  name             : string        = '';
  address          : object | null = null;
  start_at         : Date | null   = null;
  end_at           : Date | null   = null;
  notes            : string        = '';
  max_participants: number         = 0;
  is_active        : boolean       = false;
  price            : number        = 0;
  deposit          : number        = 0;
  staffer_price    : number        = 0;

  constructor () {
    super()
    this.$collectionId = '671aa09c000637511c8a'
  }
}


// * Sponsorships
export interface SponsorshipInterface {
  walk_id                  : string;
  name                     : string;
  address                  : object | null;
  phone                    : object | null;
  email                    : string | null;
  church_name              : string;
  church_denomination      : string;
  church_address           : object | null;
  weekend_community        : string;
  weekend_event            : string;
  weekend_event_at         : Date | null;
  weekend_number           : string;
  weekend_location         : string;
  pilgrim_name             : string;
  pilgrim_advocacy         : string;
  is_in_reunion_group      : boolean;
  is_praying_for_pilgrim   : boolean;
  has_prompted_spouse      : boolean;
  has_explained_walk       : boolean;
  has_explained_post_walk  : boolean;
  will_join_reunion_group  : boolean;
  will_bring_pilgrim       : boolean;
  will_attend_saturday     : boolean;
  will_attend_sunday       : boolean;
  can_care_for_family      : boolean;
  will_participate_in_vigil: boolean;
  vigil_commitment_at      : Date | null;
  paid_at                  : Date | null;
  paid_amount              : number;
};

export class SponsorshipCollection extends BaseCollection implements SponsorshipInterface {
  walk_id                   : string        = '';
  name                      : string        = '';
  address                   : object | null = null;
  phone                     : object | null = null;
  email                     : string | null = null;
  church_name               : string        = '';
  church_denomination       : string        = '';
  church_address            : object | null = null;
  weekend_community         : string        = '';
  weekend_event             : string        = '';
  weekend_event_at          : Date | null   = null;
  weekend_number            : string        = '';
  weekend_location          : string        = '';
  pilgrim_name              : string        = '';
  pilgrim_advocacy          : string        = '';
  is_in_reunion_group       : boolean       = false;
  is_praying_for_pilgrim    : boolean       = false;
  has_prompted_spouse       : boolean       = false;
  has_explained_walk        : boolean       = false;
  has_explained_post_walk   : boolean       = false;
  will_join_reunion_group   : boolean       = false;
  will_bring_pilgrim        : boolean       = false;
  will_attend_saturday      : boolean       = false;
  will_attend_sunday        : boolean       = false;
  can_care_for_family       : boolean       = false;
  will_participate_in_vigil : boolean       = false;
  vigil_commitment_at       : Date | null   = null;
  paid_at                   : Date | null   = null;
  paid_amount               : number        = 0;

  constructor () {
    super()
    this.$collectionId = '671aa0a80002a73175da'
  }
}

// * Pilgrims
export interface PilgrimInterface {
  walk_id                     : string
  name                        : string
  name_tag                    ?: string
  address                     : object | null
  phone                       : object | null
  email                       : string | null
  date_of_birth               : Date | null
  sex                         : string
  church_status               : string
  occupation                  : string
  relationship_status         : string
  number_of_children          : number
  has_spouse_attended_walk    : boolean
  has_spouse_registered_walk  : boolean
  church_name                 : string
  church_denomination         : string
  church_address              : object | null
  pastor_name                 : string
  pastor_phone                : object | null
  religious_activities        : string
  health_conditions           : string
  requested_cot_reason        : string
  insurance_provider          : string
  insurance_number            : string
  doctor_name                 : string
  doctor_phone                : object | null
  emergency_name              : string
  emergency_email             : string | null
  emergency_home_phone        : object | null
  emergency_work_phone        : object | null
  has_walk_been_explained     : boolean
  has_post_walk_been_explained: boolean
  expectations_prompt         : string
  paid_at                     : Date | null
  paid_amount                 : number
  sponsor_name                : string
  sponsor_address             : object | null
  sponsor_phone               : object | null
  sponsor_email               : string | null
}

export class PilgrimCollection extends BaseCollection implements PilgrimInterface {
  walk_id                      : string        = ''
  name                         : string        = ''
  name_tag                     : string        = ''
  address                      : object | null = null
  phone                        : object | null = null
  email                        : string | null  = null
  date_of_birth                : Date | null   = null
  sex                          : string        = ''
  church_status                : string        = ''
  occupation                   : string        = ''
  relationship_status          : string        = ''
  number_of_children           : number        = 0
  has_spouse_attended_walk     : boolean       = false
  has_spouse_registered_walk   : boolean       = false
  church_name                  : string        = ''
  church_denomination          : string        = ''
  church_address               : object | null = null
  pastor_name                  : string        = ''
  pastor_phone                 : object | null = null
  religious_activities         : string        = ''
  health_conditions            : string        = ''
  requested_cot_reason         : string        = ''
  insurance_provider           : string        = ''
  insurance_number             : string        = ''
  doctor_name                  : string        = ''
  doctor_phone                 : object | null = null
  emergency_name               : string        = ''
  emergency_email              : string | null = null
  emergency_home_phone         : object | null = null
  emergency_work_phone         : object | null = null
  has_walk_been_explained      : boolean       = false
  has_post_walk_been_explained : boolean       = false
  expectations_prompt          : string        = ''
  paid_at                      : Date | null   = null
  paid_amount                  : number        = 0
  sponsor_name                 : string        = ''
  sponsor_address              : object | null = null
  sponsor_phone                : object | null = null
  sponsor_email                : string | null  = null

  constructor () {
    super()
    this.$collectionId = '671aa0b6002925fa6a6d'
  }

  // create = (data: PilgrimInterface) => {
  //   return super.create(data);
  // }

  // update = (id: string, data: Partial<PilgrimInterface>) => {
  //   return super.update(id, data);
  // }
}


// * Staffers
export interface StafferInterface {
  walk_id             : string
  name                : string
  name_tag            : string
  email               : string | null
  address             : object | null
  date_of_birth       : Date | null
  sex                 : string
  accountability_group: string
  church_name         : string
  church_denomination : string
  church_pastor       : string
  church_address      : object | null
  health_needs        : string
  sleep_needs         : string
  dietary_needs       : string
  paid_amount         : number
  paid_at             : Date | null
  primary_phone       : object | null
  secondary_phone     : object | null
}

export class StafferCollection extends BaseCollection implements StafferInterface {
  walk_id              : string        = ''
  name                 : string        = ''
  name_tag             : string        = ''
  email                : string | null = null
  address              : object | null = null
  date_of_birth        : Date | null   = null
  sex                  : string        = ''
  accountability_group : string        = ''
  church_name          : string        = ''
  church_denomination  : string        = ''
  church_pastor        : string        = ''
  church_address       : object | null = null
  health_needs         : string        = ''
  sleep_needs          : string        = ''
  dietary_needs        : string        = ''
  paid_amount          : number        = 0
  paid_at              : Date | null   = null
  primary_phone        : object | null = null
  secondary_phone      : object | null = null

  constructor () {
    super()
    this.$collectionId = '671aa0c40026718458d9'
  }
}

// * Intercessors
export interface IntercessorInterface {
  walk_id : string
  name    : string
  phone   : object | null
  start_at: Date[] | []
}

export class IntercessorCollection extends BaseCollection implements IntercessorInterface {
  walk_id : string        = ''
  name    : string        = ''
  phone   : object | null = null
  start_at: Date[] | []   = []

  constructor () {
    super()
    this.$collectionId = '671aa1d100086c53ca49'
  }
}


// * Gatherings
export interface GatheringInterface {
  name      : string
  address   : object | null
  start_at  : Date | null
  end_at    : Date | null
  notes     : string
  is_active : boolean
}

export class GatheringCollection extends BaseCollection implements GatheringInterface {
  name       : string       = ''
  address: object | null = null
  start_at   : Date | null  = null
  end_at     : Date | null  = null
  notes      : string       = ''
  is_active  : boolean      = false

  constructor () {
    super()
    this.$collectionId = '671aa0d30032d5b111ea'
  }
}


// * Board Members
export interface BoardMemberInterface {
  name    : string
  title   : string
  position: string
  phone   : object | null
  email   : string | null
}

export class BoardMemberCollection extends BaseCollection implements BoardMemberInterface {
  name    : string        = ''
  title   : string        = ''
  position: string        = ''
  phone   : object | null = null
  email   : string | null = null

  constructor () {
    super()
    this.$collectionId = '671aa1ed0011551bf883'
  }
}


// * Files
export interface FileInterface {
  id         : string
  attachments: string[]
}

export class FileCollection extends BaseCollection implements FileInterface {
  id         : string        = ''
  attachments: string[]      = []

  constructor () {
    super()
    this.$collectionId = '671aa2130013a9040f94'
  }
}


// Todo: Emergency Addendums Collection


// * Helpers

const prepareDataForRequest = (object: { [key: string]: any }) => {
  return Object.fromEntries(
    Object.entries(object).map(([key, value]) => {
      if (Array.isArray(value)) {
        return [key, value.map(v => v instanceof Date ? prepareForAppwrite(v) : v)]
      }
      if (value instanceof Date) {
        return [key, prepareForAppwrite(value)]
      }
      if (typeof value === 'object' && value !== null) {
        return [key, JSON.stringify(value)]
      }
      if (typeof value === 'string' && !isNaN(Date.parse(value)) && isNaN(Number(value))) {
        return [key, prepareForAppwrite(value)]
      }
      return [key, value]
    })
  )
}


// * Storage
export class StorageCollection {
  /**
   * Create a file in storage
   * @param file The file to upload
   */
  static create = (file: File) => {
    try {
      return storage.createFile(appwrite_bucket_id, ID.unique(), file);
    } catch (error) {
      console.error(error);
    }
  }

  static getFileUrl = (file_id: string) => {
    try {
      return `${appwrite_endpoint}/storage/buckets/${appwrite_bucket_id}/files/${file_id}/view?project=${appwrite_project_id}`
    } catch (error) {
      console.error(error);
    }
  }

  static getFilePreviewUrl = (file_id: string) => {
    try {
      return `${appwrite_endpoint}/storage/buckets/${appwrite_bucket_id}/files/${file_id}/preview?project=${appwrite_project_id}`
    } catch (error) {
      console.error(error);
    }
  }

  static getFileInfo = async (file_id: string) => {
    try {
      const fileInfo = await storage.getFile(appwrite_bucket_id, file_id);
      if (!fileInfo) {
        console.warn('No file info returned for ID:', file_id);
        return null;
      }

      return {
        name: fileInfo.name || `File ${file_id.substring(0, 8)}...`,
        size: fileInfo.sizeOriginal || 0,
        mimeType: fileInfo.mimeType || 'application/octet-stream',
        createdAt: fileInfo.$createdAt || new Date().toISOString()
      };
    } catch (error) {
      console.error('Error getting file info:', error);
      return null;
    }
  }

  static delete = (file_id: string) => {
    try {
      return storage.deleteFile(appwrite_bucket_id, file_id);
    } catch (error) {
      console.error(error);
    }
  }
}

export const account = new Account(client);
export { ID } from 'appwrite';
